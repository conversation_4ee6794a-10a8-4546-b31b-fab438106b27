package com.ruoyi.framework.aspectj;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.DataScopeCapable;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.security.context.PermissionContextHolder;

/**
 * 数据过滤处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class DataScopeAspect
{
    /**
     * 全部数据权限
     */
    public static final String DATA_SCOPE_ALL = "1";

    /**
     * 自定数据权限
     */
    public static final String DATA_SCOPE_CUSTOM = "2";

    /**
     * 部门数据权限
     */
    public static final String DATA_SCOPE_DEPT = "3";

    /**
     * 部门及以下数据权限
     */
    public static final String DATA_SCOPE_DEPT_AND_CHILD = "4";

    /**
     * 仅本人数据权限
     */
    public static final String DATA_SCOPE_SELF = "5";

    /**
     * 数据权限过滤关键字
     */
    public static final String DATA_SCOPE = "dataScope";

    @Before("@annotation(controllerDataScope)")
    public void doBefore(JoinPoint point, DataScope controllerDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, controllerDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, DataScope controllerDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            SysUser currentUser = loginUser.getUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !currentUser.isAdmin())
            {
                String permission = StringUtils.defaultIfEmpty(controllerDataScope.permission(), PermissionContextHolder.getContext());
                dataScopeFilter(joinPoint, currentUser, controllerDataScope.deptAlias(), controllerDataScope.userAlias(), permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint 切点
     * @param user 用户
     * @param deptAlias 部门别名
     * @param userAlias 用户别名
     * @param permission 权限字符
     */
    public static void dataScopeFilter(JoinPoint joinPoint, SysUser user, String deptAlias, String userAlias, String permission)
    {
        StringBuilder sqlString = new StringBuilder();
        List<String> conditions = new ArrayList<String>();
        List<String> scopeCustomIds = new ArrayList<String>();
        
        // 增加运营人员的特殊数据过滤逻辑
        LoginUser loginUser = SecurityUtils.getLoginUser();
        
        // 如果是运营人员，并且正在查询租户表，则限定其只能查询到自己创建的租户
        if (SecurityUtils.isOperator() && "t".equals(deptAlias)) {
            sqlString.append(StringUtils.format(" AND {}.create_user_id = {} ", deptAlias, user.getUserId()));
            // 应用运营人员过滤条件
            applyDataScopeCondition(joinPoint, sqlString.toString());
            return; // 运营人员只能看自己创建的租户，直接返回
        }
        
        // 如果是运营人员查询用户列表，需要特殊处理
        if (SecurityUtils.isOperator() && "d".equals(deptAlias) && "u".equals(userAlias)) {
            // 运营人员可以查询：
            // 1. 普通的部门权限范围内的用户（通过下面的常规逻辑处理）
            // 2. 自己创建的租户的管理员用户（通过额外条件添加）
            
            // 先处理常规的部门权限
            boolean hasRegularPermissions = false;
            
            user.getRoles().forEach(role -> {
                if (DATA_SCOPE_CUSTOM.equals(role.getDataScope()) && StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
                {
                    scopeCustomIds.add(Convert.toStr(role.getRoleId()));
                }
            });

            for (SysRole role : user.getRoles())
            {
                String dataScope = role.getDataScope();
                if (conditions.contains(dataScope) || StringUtils.equals(role.getStatus(), UserConstants.ROLE_DISABLE))
                {
                    continue;
                }
                if (!StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
                {
                    continue;
                }
                if (DATA_SCOPE_ALL.equals(dataScope))
                {
                    // 如果有全部数据权限，就不需要额外的租户管理员条件了
                    sqlString = new StringBuilder();
                    conditions.add(dataScope);
                    hasRegularPermissions = true;
                    break;
                }
                else if (DATA_SCOPE_CUSTOM.equals(dataScope))
                {
                    if (scopeCustomIds.size() > 1)
                    {
                        // 多个自定数据权限使用in查询，避免多次拼接。
                        sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id in ({}) ) ", deptAlias, String.join(",", scopeCustomIds)));
                    }
                    else
                    {
                        sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias, role.getRoleId()));
                    }
                    hasRegularPermissions = true;
                }
                else if (DATA_SCOPE_DEPT.equals(dataScope))
                {
                    sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, user.getDeptId()));
                    hasRegularPermissions = true;
                }
                else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
                {
                    sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )", deptAlias, user.getDeptId(), user.getDeptId()));
                    hasRegularPermissions = true;
                }
                else if (DATA_SCOPE_SELF.equals(dataScope))
                {
                    if (StringUtils.isNotBlank(userAlias))
                    {
                        sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                    }
                    else
                    {
                        // 数据权限为仅本人且没有userAlias别名不查询任何数据
                        sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                    }
                    hasRegularPermissions = true;
                }
                conditions.add(dataScope);
            }
            
            // 为运营人员添加特殊条件：可以查询自己创建的租户的管理员用户
            // 这里添加一个OR条件，允许查询具有租户管理员角色且其租户是由当前运营人员创建的用户
            sqlString.append(StringUtils.format(" OR ({}.user_id IN ( " +
                    "SELECT ur.user_id FROM sys_user_role ur " +
                    "JOIN sys_role r ON ur.role_id = r.role_id " +
                    "JOIN sys_user su ON ur.user_id = su.user_id " +
                    "WHERE r.role_key = 'tenant' " +
                    "AND su.tenant_id IN ( " +
                        "SELECT t.tenant_id FROM qwx_tenant t WHERE t.create_user_id = {} " +
                    ") " +
                ")) ", userAlias, user.getUserId()));
            
            // 如果没有常规权限，需要确保至少有一个条件
            if (StringUtils.isEmpty(conditions))
            {
                sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
            }

            if (StringUtils.isNotBlank(sqlString.toString()))
            {
                applyDataScopeCondition(joinPoint, " AND (" + sqlString.substring(4) + ")");
            }
            return; // 运营人员用户查询特殊处理完成，直接返回
        }
        
        user.getRoles().forEach(role -> {
            if (DATA_SCOPE_CUSTOM.equals(role.getDataScope()) && StringUtils.equals(role.getStatus(), UserConstants.ROLE_NORMAL) && StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
            {
                scopeCustomIds.add(Convert.toStr(role.getRoleId()));
            }
        });

        for (SysRole role : user.getRoles())
        {
            String dataScope = role.getDataScope();
            if (conditions.contains(dataScope) || StringUtils.equals(role.getStatus(), UserConstants.ROLE_DISABLE))
            {
                continue;
            }
            if (!StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission)))
            {
                continue;
            }
            if (DATA_SCOPE_ALL.equals(dataScope))
            {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }
            else if (DATA_SCOPE_CUSTOM.equals(dataScope))
            {
                if (scopeCustomIds.size() > 1)
                {
                    // 多个自定数据权限使用in查询，避免多次拼接。
                    sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id in ({}) ) ", deptAlias, String.join(",", scopeCustomIds)));
                }
                else
                {
                    sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias, role.getRoleId()));
                }
            }
            else if (DATA_SCOPE_DEPT.equals(dataScope))
            {
                sqlString.append(StringUtils.format(" OR {}.dept_id = {} ", deptAlias, user.getDeptId()));
            }
            else if (DATA_SCOPE_DEPT_AND_CHILD.equals(dataScope))
            {
                sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = {} or find_in_set( {} , ancestors ) )", deptAlias, user.getDeptId(), user.getDeptId()));
            }
            else if (DATA_SCOPE_SELF.equals(dataScope))
            {
                if (StringUtils.isNotBlank(userAlias))
                {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                }
            }
            conditions.add(dataScope);
        }

        // 角色都不包含传递过来的权限字符，这个时候sqlString也会为空，所以要限制一下,不查询任何数据
        if (StringUtils.isEmpty(conditions))
        {
            sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            applyDataScopeCondition(joinPoint, " AND (" + sqlString.substring(4) + ")");
        }
    }

    /**
     * 应用数据权限条件到参数对象
     * 支持BaseEntity和DataScopeCapable接口
     */
    private static void applyDataScopeCondition(JoinPoint joinPoint, String condition)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params))
        {
            Map<String, Object> paramsMap = null;
            
            // 优先检查是否实现了DataScopeCapable接口
            if (params instanceof DataScopeCapable)
            {
                DataScopeCapable dataScopeCapable = (DataScopeCapable) params;
                paramsMap = dataScopeCapable.getParams();
                if (paramsMap == null) {
                    paramsMap = new HashMap<>();
                    dataScopeCapable.setParams(paramsMap);
                }
            }
            // 兼容原有的BaseEntity
            else if (params instanceof BaseEntity)
            {
                BaseEntity baseEntity = (BaseEntity) params;
                paramsMap = baseEntity.getParams();
            }
            
            // 应用数据权限条件
            if (paramsMap != null) {
                paramsMap.put(DATA_SCOPE, condition);
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params))
        {
            Map<String, Object> paramsMap = null;
            
            // 优先检查是否实现了DataScopeCapable接口
            if (params instanceof DataScopeCapable)
            {
                DataScopeCapable dataScopeCapable = (DataScopeCapable) params;
                paramsMap = dataScopeCapable.getParams();
                if (paramsMap == null) {
                    paramsMap = new HashMap<>();
                    dataScopeCapable.setParams(paramsMap);
                }
            }
            // 兼容原有的BaseEntity
            else if (params instanceof BaseEntity)
            {
                BaseEntity baseEntity = (BaseEntity) params;
                paramsMap = baseEntity.getParams();
            }
            
            // 清空数据权限条件
            if (paramsMap != null) {
                paramsMap.put(DATA_SCOPE, "");
            }
        }
    }
}
