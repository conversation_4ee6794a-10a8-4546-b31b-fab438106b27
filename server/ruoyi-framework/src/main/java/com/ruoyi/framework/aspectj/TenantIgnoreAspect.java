package com.ruoyi.framework.aspectj;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import com.ruoyi.common.annotation.TenantIgnore;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.config.MybatisPlusConfig;
import lombok.extern.slf4j.Slf4j;
import java.lang.reflect.Method;

/**
 * 租户过滤忽略切面
 * 针对运营人员的特定操作，自动忽略多租户过滤
 * 
 * <AUTHOR>
 */
@Aspect
@Component
@Slf4j
public class TenantIgnoreAspect {

    /**
     * 针对获取路由信息的接口，运营人员需要看到所有菜单
     */
    @Around("execution(* com.ruoyi.web.controller.system.SysLoginController.getRouters(..))")
    public Object aroundGetRouters(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithTenantIgnore(joinPoint, "获取路由信息");
    }

    /**
     * 针对菜单树查询，运营人员需要看到所有菜单
     */
    @Around("execution(* com.ruoyi.system.service.impl.SysMenuServiceImpl.selectMenuTreeByUserId(..))")
    public Object aroundSelectMenuTreeByUserId(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithTenantIgnore(joinPoint, "查询菜单树");
    }

    /**
     * 针对租户查询，运营人员需要看到相关租户数据
     */
    @Around("execution(* com.ruoyi.app.service.impl.QwxTenantServiceImpl.selectQwxTenantList(..))")
    public Object aroundSelectTenantList(ProceedingJoinPoint joinPoint) throws Throwable {
        return executeWithTenantIgnore(joinPoint, "查询租户列表");
    }

    /**
     * 处理带有 @TenantIgnore 注解的方法
     */
    @Around("@annotation(com.ruoyi.common.annotation.TenantIgnore)")
    public Object aroundTenantIgnoreAnnotation(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        TenantIgnore tenantIgnore = method.getAnnotation(TenantIgnore.class);
        
        String operation = tenantIgnore.value().isEmpty() ? 
            method.getDeclaringClass().getSimpleName() + "." + method.getName() : 
            tenantIgnore.value();
            
        return executeWithTenantIgnoreAnnotation(joinPoint, operation, tenantIgnore.operatorOnly());
    }

    /**
     * 执行带租户忽略的方法
     */
    private Object executeWithTenantIgnore(ProceedingJoinPoint joinPoint, String operation) throws Throwable {
        return executeWithTenantIgnoreAnnotation(joinPoint, operation, true);
    }

    /**
     * 执行带租户忽略的方法（支持注解参数）
     */
    private Object executeWithTenantIgnoreAnnotation(ProceedingJoinPoint joinPoint, String operation, boolean operatorOnly) throws Throwable {
        boolean shouldIgnore = false;
        
        try {
            // 根据operatorOnly参数决定是否检查用户权限
            if (operatorOnly) {
                // 只有运营人员或超级管理员才忽略租户过滤
                if (SecurityUtils.isSuperAdmin()) {
                    MybatisPlusConfig.ignoreTenant();
                    shouldIgnore = true;
                    log.debug("运营人员/超级管理员执行 {} 操作，已忽略多租户过滤", operation);
                }
            } else {
                // 所有用户都忽略租户过滤
                MybatisPlusConfig.ignoreTenant();
                shouldIgnore = true;
                log.debug("所有用户执行 {} 操作，已忽略多租户过滤", operation);
            }
            
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("执行 {} 操作时发生异常: {}", operation, e.getMessage(), e);
            throw e;
        } finally {
            if (shouldIgnore) {
                MybatisPlusConfig.clearTenantIgnore();
                log.debug("已清除多租户过滤忽略状态");
            }
        }
    }
} 