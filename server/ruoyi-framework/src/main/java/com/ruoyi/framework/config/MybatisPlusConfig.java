package com.ruoyi.framework.config;

import java.util.Arrays;
import java.util.List;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * Mybatis Plus 配置
 *
 * <AUTHOR>
 */
@EnableTransactionManagement(proxyTargetClass = true)
@Configuration
public class MybatisPlusConfig
{
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor()
    {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 多租户拦截器 - 必须放在分页插件之前
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public Expression getTenantId() {
                try {
                    // 从安全上下文中获取当前登录用户的租户ID
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    if (loginUser == null || loginUser.getUser() == null || loginUser.getUser().getTenantId() == null) {
                        return null; // 没有租户信息，不进行过滤
                    }
                    
                    // 如果是超级管理员，则不进行租户过滤
                    if (SecurityUtils.isAdmin(loginUser.getUserId())) {
                        return null; 
                    }
                    
                    return new LongValue(loginUser.getUser().getTenantId());
                } catch (Exception e) {
                    // 在登录过程中或其他无安全上下文的情况下，不进行租户过滤
                    return null;
                }
            }

            @Override
            public String getTenantIdColumn() {
                return "tenant_id"; // 默认的租户字段名
            }

            @Override
            public boolean ignoreTable(String tableName) {
                // 配置不需要租户过滤的表（全局表）
                List<String> ignoreTables = Arrays.asList(
                    "qwx_tenant", "qwx_global_user", "qwx_user_tenant", "sys_config", 
                    "sys_dict_type", "sys_dict_data", "sys_role", "sys_menu", "sys_dept",
                    "sys_post", "sys_role_menu", "sys_role_dept", "sys_user_role", "sys_user_post",
                    "qrtz_", "gen_table", "gen_table_column", "sys_oper_log", "sys_logininfor",
                    "sys_job", "sys_job_log", "sys_notice"
                );
                
                // 登录时不能过滤用户表
                if ("sys_user".equalsIgnoreCase(tableName)) {
                    try {
                        SecurityUtils.getLoginUser();
                        // 如果能获取到登录用户，说明不是在登录过程中，可以进行过滤
                        return false;
                    } catch (Exception e) {
                        // 登录过程中或无安全上下文，不进行过滤
                        return true;
                    }
                }
                
                // 检查表名是否在忽略列表中
                for (String ignoreTable : ignoreTables) {
                    if (tableName.toLowerCase().startsWith(ignoreTable.toLowerCase())) {
                        return true;
                    }
                }
                
                return false;
            }
        }));
        
        // 分页插件
        interceptor.addInnerInterceptor(paginationInnerInterceptor());
        // 乐观锁插件
        interceptor.addInnerInterceptor(optimisticLockerInnerInterceptor());
        // 阻断插件
        interceptor.addInnerInterceptor(blockAttackInnerInterceptor());
        return interceptor;
    }

    /**
     * 分页插件，自动识别数据库类型 https://baomidou.com/guide/interceptor-pagination.html
     */
    public PaginationInnerInterceptor paginationInnerInterceptor()
    {
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        // 设置数据库类型为mysql
        paginationInnerInterceptor.setDbType(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(-1L);
        return paginationInnerInterceptor;
    }

    /**
     * 乐观锁插件 https://baomidou.com/guide/interceptor-optimistic-locker.html
     */
    public OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor()
    {
        return new OptimisticLockerInnerInterceptor();
    }

    /**
     * 如果是对全表的删除或更新操作，就会终止该操作 https://baomidou.com/guide/interceptor-block-attack.html
     */
    public BlockAttackInnerInterceptor blockAttackInnerInterceptor()
    {
        return new BlockAttackInnerInterceptor();
    }
}