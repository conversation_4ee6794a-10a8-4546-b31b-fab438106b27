package com.ruoyi.common.core.domain;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据权限上下文
 * 使用ThreadLocal存储数据权限过滤条件，避免实体类需要继承BaseEntity
 * 
 * <AUTHOR>
 */
public class DataScopeContext {
    
    private static final ThreadLocal<Map<String, Object>> CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置数据权限条件
     * 
     * @param key 键
     * @param value 值
     */
    public static void put(String key, Object value) {
        Map<String, Object> map = CONTEXT.get();
        if (map == null) {
            map = new HashMap<>();
            CONTEXT.set(map);
        }
        map.put(key, value);
    }
    
    /**
     * 获取数据权限条件
     * 
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        Map<String, Object> map = CONTEXT.get();
        return map != null ? map.get(key) : null;
    }
    
    /**
     * 获取所有数据权限条件
     * 
     * @return 条件Map
     */
    public static Map<String, Object> getAll() {
        Map<String, Object> map = CONTEXT.get();
        return map != null ? new HashMap<>(map) : new HashMap<>();
    }
    
    /**
     * 清空数据权限条件
     */
    public static void clear() {
        CONTEXT.remove();
    }
    
    /**
     * 清空指定键的数据权限条件
     * 
     * @param key 键
     */
    public static void remove(String key) {
        Map<String, Object> map = CONTEXT.get();
        if (map != null) {
            map.remove(key);
            if (map.isEmpty()) {
                CONTEXT.remove();
            }
        }
    }
} 