package com.ruoyi.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 租户过滤忽略注解
 * 标记此注解的方法在执行时会忽略多租户过滤
 * 
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TenantIgnore {
    
    /**
     * 是否只对运营人员生效
     * true: 只有运营人员/超级管理员才忽略租户过滤
     * false: 所有用户都忽略租户过滤
     */
    boolean operatorOnly() default true;
    
    /**
     * 描述信息
     */
    String value() default "";
} 