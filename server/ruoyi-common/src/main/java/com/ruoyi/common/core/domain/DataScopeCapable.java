package com.ruoyi.common.core.domain;

import java.util.Map;

/**
 * 数据权限能力接口
 * 实现此接口的类可以支持数据权限过滤，而无需继承BaseEntity
 * 
 * <AUTHOR>
 */
public interface DataScopeCapable {
    
    /**
     * 获取请求参数Map
     * 用于存储数据权限过滤条件
     * 
     * @return 参数Map
     */
    Map<String, Object> getParams();
    
    /**
     * 设置请求参数Map
     * 
     * @param params 参数Map
     */
    void setParams(Map<String, Object> params);
} 