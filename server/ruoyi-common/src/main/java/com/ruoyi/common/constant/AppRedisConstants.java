package com.ruoyi.common.constant;

/**
 * App Redis 常量
 * <AUTHOR>
 * @date 2025/5/11 下午10:43
 */
public class AppRedisConstants {
    
    /** 验证码有效期（分钟） */
    public static final int CODE_EXPIRE_MINUTES = 5;
    
    /** 短信验证码类型 */
    public static final String CODE_TYPE_SMS = "sms";
    
    /** 邮箱验证码类型 */
    public static final String CODE_TYPE_EMAIL = "email";
    
    /** Redis短信验证码前缀 */
    public static final String SMS_CODE_PREFIX = "app:sms:code:";
    
    /** Redis邮箱验证码前缀 */
    public static final String EMAIL_CODE_PREFIX = "app:email:code:";
    
    /** APP用户Token前缀 */
    public static final String APP_TOKEN_PREFIX = "app:token:";
    
    /** APP用户Token黑名单前缀 */    
    public static final String APP_TOKEN_BLACKLIST = "app:token:blacklist:";
    
    /** APP租户Token前缀 */
    public static final String APP_TENANT_TOKEN_PREFIX = "app:tenant:token:";
    
    /** APP租户Token黑名单前缀 */
    public static final String APP_TENANT_TOKEN_BLACKLIST = "app:tenant:token:blacklist:";
    
    /** APP刷新Token前缀 */
    public static final String APP_REFRESH_TOKEN_PREFIX = "app:refresh:token:";
}
