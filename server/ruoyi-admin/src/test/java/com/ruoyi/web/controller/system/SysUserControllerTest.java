package com.ruoyi.web.controller.system;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.service.ISysUserService;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户控制器测试类
 * 用于测试用户查询接口的修复
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class SysUserControllerTest {

    @Autowired
    private SysUserController sysUserController;
    
    @Autowired
    private ISysUserService userService;

    /**
     * 测试用户列表查询
     */
    @Test
    public void testUserList() {
        // 创建查询参数
        SysUser user = new SysUser();
        
        // 调用查询接口
        TableDataInfo result = sysUserController.list(user);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getRows());
        
        System.out.println("查询结果总数: " + result.getTotal());
        System.out.println("查询结果行数: " + result.getRows().size());
        
        // 验证查询结果不为空（至少应该有一些用户数据）
        assertTrue(result.getTotal() >= 0, "查询结果总数应该大于等于0");
    }

    /**
     * 测试运营人员查询租户管理员用户
     */
    @Test
    public void testOperatorQueryTenantAdminUsers() {
        // 创建查询参数
        SysUser user = new SysUser();
        
        // 直接调用service层方法进行测试
        try {
            var userList = userService.selectUserList(user);
            assertNotNull(userList);
            
            System.out.println("Service层查询结果数量: " + userList.size());
            
            // 打印用户信息用于调试
            for (SysUser u : userList) {
                System.out.println("用户ID: " + u.getUserId() + 
                                 ", 用户名: " + u.getUserName() + 
                                 ", 租户ID: " + u.getTenantId() +
                                 ", 部门ID: " + u.getDeptId());
            }
            
        } catch (Exception e) {
            System.err.println("查询用户列表时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
