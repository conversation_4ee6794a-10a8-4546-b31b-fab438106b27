package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.Date;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.service.ImGroupService;
import com.ruoyi.app.domain.ImGroup;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.dto.DissolveGroupRequestDto;
import com.ruoyi.app.model.vo.GroupStatisticsResponseVo;
import com.ruoyi.app.model.vo.OnlineMemberCountResponseVo;
import com.ruoyi.web.service.GroupApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * IM群组管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/im/group")
public class ImGroupController extends BaseController {

    @Resource
    private ImGroupService imGroupService;
    @Resource
    private GroupApiService groupApiService;

    /**
     * 查询群组列表
     */
    @PreAuthorize("@ss.hasPermi('im:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(required = false) String groupName,
                             @RequestParam(required = false) String groupType,
                             @RequestParam(required = false) String status,
                             @RequestParam(required = false) Long tenantId) {
        startPage();
        List<ImGroup> list = imGroupService.selectGroupList(groupName, groupType, status, tenantId);
        
        return getDataTable(list);
    }

    /**
     * 导出群组列表
     */
    @PreAuthorize("@ss.hasPermi('im:group:list')")
    @Log(title = "群组管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                      @RequestParam(required = false) String groupName,
                      @RequestParam(required = false) String groupType,
                      @RequestParam(required = false) String status,
                      @RequestParam(required = false) Long tenantId) {
        List<ImGroup> list = imGroupService.selectGroupList(groupName, groupType, status, tenantId);
        ExcelUtil<ImGroup> util = new ExcelUtil<>(ImGroup.class);
        util.exportExcel(response, list, "群组数据");
    }

    /**
     * 获取群组详细信息
     */
    @PreAuthorize("@ss.hasPermi('im:group:view')")
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") Long groupId) {
        ImGroup group = imGroupService.getById(groupId);
        if (group == null) {
            return error("群组不存在");
        }
        
        return success(group);
    }

    /**
     * 批量查询群组信息列表
     */
    @PreAuthorize("@ss.hasPermi('im:group:view')")
    @PostMapping("/batch")
    public AjaxResult getBatchInfo(@RequestBody List<Long> groupIds) {
        List<ImGroup> groups = imGroupService.selectGroupByIds(groupIds);
        
        return success(groups);
    }

    /**
     * 创建群组
     */
    @PreAuthorize("@ss.hasPermi('im:group:add')")
    @Log(title = "创建群组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CreateGroupRequestDto createGroupRequest) {
        try {
            int result = groupApiService.createGroup(createGroupRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("创建群组失败: " + e.getMessage());
        }
    }

    /**
     * 更新群组信息
     */
    @PreAuthorize("@ss.hasPermi('im:group:edit')")
    @Log(title = "更新群组信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody UpdateGroupRequestDto updateGroupRequest) {
        try {
            int result = groupApiService.updateGroup(updateGroupRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("更新群组信息失败: " + e.getMessage());
        }
    }

    /**
     * 转让群主
     */
    @PreAuthorize("@ss.hasPermi('im:group:transfer')")
    @Log(title = "转让群主", businessType = BusinessType.UPDATE)
    @PutMapping("/transfer")
    public AjaxResult transferOwner(@RequestBody TransferOwnerRequestDto transferRequest) {
        try {
            int result = groupApiService.transferOwner(transferRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("转让群主失败: " + e.getMessage());
        }
    }

    /**
     * 添加群管理员
     */
    @PreAuthorize("@ss.hasPermi('im:group:setAdmin')")
    @Log(title = "添加群管理员", businessType = BusinessType.UPDATE)
    @PutMapping("/admin/add")
    public AjaxResult addAdmin(@RequestBody AdminManageRequestDto addAdminRequest) {
        try {
            int result = groupApiService.addGroupAdmin(addAdminRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("添加管理员失败: " + e.getMessage());
        }
    }

    /**
     * 移除群管理员
     */
    @PreAuthorize("@ss.hasPermi('im:group:removeAdmin')")
    @Log(title = "移除群管理员", businessType = BusinessType.UPDATE)
    @PutMapping("/admin/remove")
    public AjaxResult removeAdmin(@RequestBody AdminManageRequestDto removeAdminRequest) {
        try {
            int result = groupApiService.removeGroupAdmin(removeAdminRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("移除管理员失败: " + e.getMessage());
        }
    }

    /**
     * 解散群组
     */
    @PreAuthorize("@ss.hasPermi('im:group:dissolve')")
    @Log(title = "解散群组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupId}")
    public AjaxResult dissolve(@PathVariable("groupId") Long groupId, 
                              @RequestBody(required = false) DissolveGroupRequestDto dissolveRequest) {
        try {
            // 如果没有提供请求体，创建一个默认的请求对象
            if (dissolveRequest == null) {
                dissolveRequest = new DissolveGroupRequestDto();
                dissolveRequest.setGroupId(groupId);
                dissolveRequest.setTeamType(1); // 默认为高级群
            } else {
                dissolveRequest.setGroupId(groupId); // 确保群组ID设置正确
            }
            
            int result = groupApiService.dissolveGroup(groupId, dissolveRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("解散群组失败: " + e.getMessage());
        }
    }



    /**
     * 查询高级群在线成员列表
     */
    @PreAuthorize("@ss.hasPermi('im:group:onlineList')")
    @GetMapping("/{groupId}/online")
    public AjaxResult getOnlineMembers(@PathVariable("groupId") Long groupId) {
        try {
            List<ImGroupMember> onlineMembers = imGroupService.getOnlineMembers(groupId);
            
            return success(onlineMembers);
        } catch (Exception e) {
            return error("查询在线成员失败: " + e.getMessage());
        }
    }

    /**
     * 批量查询高级群在线成员数
     */
    @PreAuthorize("@ss.hasPermi('im:group:onlineList')")
    @PostMapping("/online/count")
    public AjaxResult getOnlineMemberCount(@RequestBody List<Long> groupIds) {
        try {
            List<Object> onlineCounts = imGroupService.getOnlineMemberCount(groupIds);
            
            // 转换为VO对象
            List<OnlineMemberCountResponseVo> voList = new ArrayList<>();
            for (Object obj : onlineCounts) {
                @SuppressWarnings("unchecked")
                Map<String, Object> map = (Map<String, Object>) obj;
                OnlineMemberCountResponseVo vo = new OnlineMemberCountResponseVo();
                vo.setGroupId((Long) map.get("groupId"));
                vo.setOnlineCount((Long) map.get("onlineCount"));
                vo.setTotalCount((Long) map.get("totalCount"));
                voList.add(vo);
            }
            
            return success(voList);
        } catch (Exception e) {
            return error("查询在线成员数失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询群成员列表
     */
    @PreAuthorize("@ss.hasPermi('im:group:memberPage')")
    @GetMapping("/{groupId}/members")
    public TableDataInfo getMembers(@PathVariable("groupId") Long groupId,
                                   @RequestParam(required = false) String keyword,
                                   @RequestParam(required = false) String role,
                                   @RequestParam(required = false) String status) {
        startPage();
        List<ImGroupMember> list = imGroupService.selectGroupMembers(groupId, keyword, role, status);
        
        return getDataTable(list);
    }

    /**
     * 群组统计信息
     */
    @PreAuthorize("@ss.hasPermi('im:group:view')")
    @GetMapping("/{groupId}/statistics")
    public AjaxResult getStatistics(@PathVariable("groupId") Long groupId) {
        try {
            GroupStatisticsResponseVo vo = imGroupService.getGroupStatistics(groupId);
            if (vo == null) {
                return error("群组不存在");
            }
        
            return success(vo);
        } catch (Exception e) {
            return error("查询群组统计信息失败: " + e.getMessage());
        }
    }
} 