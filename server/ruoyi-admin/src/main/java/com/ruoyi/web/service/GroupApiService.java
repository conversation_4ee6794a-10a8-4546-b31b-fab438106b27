package com.ruoyi.web.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.domain.ImGroup;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.service.ImGroupMemberService;
import com.ruoyi.app.service.ImGroupService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.web.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 群组API服务层
 *
 * <AUTHOR>
 * @date 2025/6/1 上午11:00
 */
@Slf4j
@Service
public class GroupApiService {

    @Resource
    private ImGroupService imGroupService;

    @Resource
    private WyImService wyImService;

    @Resource
    private ImGroupMemberService groupMemberService;
    /**
     * 创建群组
     *
     * @param request 创建群组请求
     * @return 创建结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int createGroup(CreateGroupRequestDto request) {
        try {
            // 1. 构造群主的网易云信账号ID（格式为 {用户id}_{租户id}）
            String ownerYxAccountId = request.getOwnerId() + "_" + request.getTenantId();


            // 4. 构建网易云信创建群组请求
            YunxinCreateGroupRequestDto yunxinRequest = new YunxinCreateGroupRequestDto();
            yunxinRequest.setOwnerAccountId(ownerYxAccountId);
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(request.getGroupType()) ? Integer.parseInt(request.getGroupType()) : 1);
            yunxinRequest.setName(request.getGroupName());
            yunxinRequest.setIcon(request.getGroupAvatar());
            yunxinRequest.setAnnouncement(request.getAnnouncement());
            yunxinRequest.setIntro(request.getIntroduction());
            yunxinRequest.setMembersLimit(request.getMaxMemberCount());
            yunxinRequest.setServerExtension(request.getExtension());
            yunxinRequest.setCustomerExtension(request.getCustomField());
            yunxinRequest.setInviteMsg("欢迎加入群组：" + request.getGroupName());

            List<String> inviteAccountIds = new ArrayList<>();
            for (Long memberId : request.getMemberIds()) {
                inviteAccountIds.add(memberId + "_" + request.getTenantId());
            }
            yunxinRequest.setInviteAccountIds(inviteAccountIds);

            // 设置群组配置
            YunxinCreateGroupRequestDto.Configuration config = new YunxinCreateGroupRequestDto.Configuration();
            config.setJoinMode(StringUtils.isNotEmpty(request.getJoinMode()) ? Integer.parseInt(request.getJoinMode()) : 1);
            config.setAgreeMode(StringUtils.isNotEmpty(request.getBeInviteMode()) ? Integer.parseInt(request.getBeInviteMode()) : 0);
            config.setInviteMode(StringUtils.isNotEmpty(request.getInviteMode()) ? Integer.parseInt(request.getInviteMode()) : 0);
            config.setUpdateTeamInfoMode(StringUtils.isNotEmpty(request.getUpdateInfoMode()) ? Integer.parseInt(request.getUpdateInfoMode()) : 0);
            config.setUpdateExtensionMode(0); // 默认仅群主和管理员可修改扩展信息
            yunxinRequest.setConfiguration(config);

            // 5. 调用网易云信API创建群组
            String yxGroupId = wyImService.createYunxinGroup(yunxinRequest);

            if (StringUtils.isEmpty(yxGroupId)) {
                throw new ServiceException("创建网易云信群组失败，未返回群组ID");
            }

            // 6. 将网易云信群组ID设置到请求对象中
            request.setYxGroupId(yxGroupId);

            // 7. 调用本地服务保存群组信息
            return imGroupService.createGroup(request);

        } catch (ServiceException e) {
            log.error("创建群组失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建群组异常", e);
            throw new ServiceException("创建群组失败: " + e.getMessage());
        }
    }

    /**
     * 更新群组
     *
     * @param request 更新群组请求
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateGroup(UpdateGroupRequestDto request) {
        try {
            // 1. 获取现有群组信息
            ImGroup existGroup = imGroupService.getById(request.getGroupId());
            if (existGroup == null) {
                throw new ServiceException("群组不存在");
            }

            // 2. 检查是否有网易云信群组ID
            if (StringUtils.isEmpty(existGroup.getYxGroupId())) {
                throw new ServiceException("群组未关联网易云信，无法更新");
            }

            // 3. 构建网易云信更新群组请求
            YunxinUpdateGroupRequestDto yunxinRequest = new YunxinUpdateGroupRequestDto();

            // 设置群组类型（必填）
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(existGroup.getGroupType()) ?
                    Integer.parseInt(existGroup.getGroupType()) : 1);

            // 设置基本信息
            if (StringUtils.isNotEmpty(request.getGroupName())) {
                yunxinRequest.setName(request.getGroupName());
            }
            if (StringUtils.isNotEmpty(request.getGroupAvatar())) {
                yunxinRequest.setIcon(request.getGroupAvatar());
            }
            if (StringUtils.isNotEmpty(request.getAnnouncement())) {
                yunxinRequest.setAnnouncement(request.getAnnouncement());
            }
            if (StringUtils.isNotEmpty(request.getIntroduction())) {
                yunxinRequest.setIntro(request.getIntroduction());
            }
            if (StringUtils.isNotEmpty(request.getExtension())) {
                yunxinRequest.setServerExtension(request.getExtension());
            }
            if (StringUtils.isNotEmpty(request.getCustomField())) {
                yunxinRequest.setCustomerExtension(request.getCustomField());
            }

            // 设置群组配置
            YunxinUpdateGroupRequestDto.Configuration config = new YunxinUpdateGroupRequestDto.Configuration();
            if (StringUtils.isNotEmpty(request.getJoinMode())) {
                config.setJoinMode(Integer.parseInt(request.getJoinMode()));
            }
            if (StringUtils.isNotEmpty(request.getBeInviteMode())) {
                config.setAgreeMode(Integer.parseInt(request.getBeInviteMode()));
            }
            if (StringUtils.isNotEmpty(request.getInviteMode())) {
                config.setInviteMode(Integer.parseInt(request.getInviteMode()));
            }
            if (StringUtils.isNotEmpty(request.getUpdateInfoMode())) {
                config.setUpdateTeamInfoMode(Integer.parseInt(request.getUpdateInfoMode()));
            }
            // 设置全员禁言状态
            if (request.getMuteAll() != null) {
                config.setChatBannedMode(request.getMuteAll() ? 1 : 0);
            }
            config.setUpdateExtensionMode(0); // 默认仅群主和管理员可修改扩展信息
            yunxinRequest.setConfiguration(config);

            // 4. 调用网易云信API更新群组
            boolean updateSuccess = wyImService.updateYunxinGroup(existGroup.getYxGroupId(), yunxinRequest);

            if (!updateSuccess) {
                throw new ServiceException("更新网易云信群组失败");
            }

            // 5. 只有当网易云信API返回成功后，才更新本地数据库
            int result = imGroupService.updateGroup(request);

            if (result > 0) {
                log.info("群组更新成功 - 群组ID: {}, 网易云信群组ID: {}",
                        request.getGroupId(), existGroup.getYxGroupId());
            }

            return result;

        } catch (ServiceException e) {
            log.error("更新群组失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("更新群组异常", e);
            throw new ServiceException("更新群组失败: " + e.getMessage());
        }
    }

    /**
     * 转让群主
     *
     * @param request 转让群主请求
     * @return 转让结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int transferOwner(TransferOwnerRequestDto request) {
        try {
            // 1. 获取现有群组信息
            ImGroup existGroup = imGroupService.getById(request.getGroupId());
            if (existGroup == null) {
                throw new ServiceException("群组不存在");
            }

            // 2. 检查是否有网易云信群组ID
            if (StringUtils.isEmpty(existGroup.getYxGroupId())) {
                throw new ServiceException("群组未关联网易云信，无法转让群主");
            }

            // 检查新群主是否为群成员
            LambdaQueryWrapper<ImGroupMember> memberWrapper = Wrappers.lambdaQuery();
            memberWrapper.eq(ImGroupMember::getGroupId, request.getGroupId())
                    .eq(ImGroupMember::getUserId, request.getNewOwnerId());
            ImGroupMember newOwnerMember = groupMemberService.getOne(memberWrapper);
            if (newOwnerMember == null) {
                throw new RuntimeException("新群主必须是群成员");
            }

            // 3. 构造新群主的网易云信账号ID（格式为 {用户id}_{租户id}）
            String newOwnerYxAccountId = request.getNewOwnerId() + "_" + existGroup.getTenantId();

            // 4. 构建网易云信转让群主请求
            YunxinTransferOwnerRequestDto yunxinRequest = new YunxinTransferOwnerRequestDto();

            // 设置群组类型（必填）
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(existGroup.getGroupType()) ?
                    Integer.parseInt(existGroup.getGroupType()) : 1);

            // 设置新群主的云信账号ID
            yunxinRequest.setNewOwnerAccountId(newOwnerYxAccountId);

            // 设置是否退群（"1"表示退出，"0"或其他表示不退出）
            yunxinRequest.setLeave("1".equals(request.getLeaveGroup()) ? 1 : 2);

            // 5. 调用网易云信API转让群主
            boolean transferSuccess = wyImService.transferYunxinGroupOwner(existGroup.getYxGroupId(), yunxinRequest);

            if (!transferSuccess) {
                throw new ServiceException("转让网易云信群主失败");
            }
            request.setOldOwnerId(existGroup.getOwnerId()); // 设置原群主ID
            // 6. 只有当网易云信API返回成功后，才更新本地数据库
            int result = imGroupService.transferOwner(request);

            if (result > 0) {
                log.info("群主转让成功 - 群组ID: {}, 网易云信群组ID: {}, 新群主ID: {}, 是否退群: {}",
                        request.getGroupId(), existGroup.getYxGroupId(), request.getNewOwnerId(), request.getLeaveGroup());
            }

            return result;

        } catch (ServiceException e) {
            log.error("转让群主失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("转让群主异常", e);
            throw new ServiceException("转让群主失败: " + e.getMessage());
        }
    }

    /**
     * 添加群管理员
     *
     * @param request 添加管理员请求
     * @return 添加结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int addGroupAdmin(AdminManageRequestDto request) {
        try {
            // 1. 获取现有群组信息
            ImGroup existGroup = imGroupService.getById(request.getGroupId());
            if (existGroup == null) {
                throw new ServiceException("群组不存在");
            }

            // 2. 检查是否有网易云信群组ID
            if (StringUtils.isEmpty(existGroup.getYxGroupId())) {
                throw new ServiceException("群组未关联网易云信，无法添加管理员");
            }

            // 3. 构造管理员的网易云信账号ID列表（格式为 {用户id}_{租户id}）
            List<String> managerYxAccountIds = new ArrayList<>();
            for (Long userId : request.getUserIds()) {
                managerYxAccountIds.add(userId + "_" + existGroup.getTenantId());
            }

            // 4. 构建网易云信添加管理员请求
            YunxinAddManagerRequestDto yunxinRequest = new YunxinAddManagerRequestDto();

            // 设置群组类型（必填）
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(existGroup.getGroupType()) ?
                    Integer.parseInt(existGroup.getGroupType()) : 1);

            // 设置管理员列表
            yunxinRequest.setManagers(managerYxAccountIds);

            // 5. 调用网易云信API添加管理员
            boolean addSuccess = wyImService.addYunxinGroupManager(existGroup.getYxGroupId(), yunxinRequest);

            if (!addSuccess) {
                throw new ServiceException("添加网易云信群管理员失败");
            }

            // 6. 只有当网易云信API返回成功后，才更新本地数据库
            int result = imGroupService.addAdmin(request);

            if (result > 0) {
                log.info("群管理员添加成功 - 群组ID: {}, 网易云信群组ID: {}, 管理员ID列表: {}",
                        request.getGroupId(), existGroup.getYxGroupId(), request.getUserIds());
            }

            return result;

        } catch (ServiceException e) {
            log.error("添加群管理员失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("添加群管理员异常", e);
            throw new ServiceException("添加群管理员失败: " + e.getMessage());
        }
    }

    /**
     * 移除群管理员
     *
     * @param request 移除管理员请求
     * @return 移除结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int removeGroupAdmin(AdminManageRequestDto request) {
        try {
            // 1. 获取现有群组信息
            ImGroup existGroup = imGroupService.getById(request.getGroupId());
            if (existGroup == null) {
                throw new ServiceException("群组不存在");
            }

            // 2. 检查是否有网易云信群组ID
            if (StringUtils.isEmpty(existGroup.getYxGroupId())) {
                throw new ServiceException("群组未关联网易云信，无法移除管理员");
            }

            // 3. 构造管理员的网易云信账号ID列表（格式为 {用户id}_{租户id}）
            List<String> managerYxAccountIds = new ArrayList<>();
            for (Long userId : request.getUserIds()) {
                managerYxAccountIds.add(userId + "_" + existGroup.getTenantId());
            }

            // 4. 构建网易云信移除管理员请求
            YunxinRemoveManagerRequestDto yunxinRequest = new YunxinRemoveManagerRequestDto();

            // 设置群组类型（必填）
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(existGroup.getGroupType()) ?
                    Integer.parseInt(existGroup.getGroupType()) : 1);

            // 设置管理员列表
            yunxinRequest.setManagers(managerYxAccountIds);

            // 5. 调用网易云信API移除管理员
            boolean removeSuccess = wyImService.removeYunxinGroupManager(existGroup.getYxGroupId(), yunxinRequest);

            if (!removeSuccess) {
                throw new ServiceException("移除网易云信群管理员失败");
            }

            // 6. 只有当网易云信API返回成功后，才更新本地数据库
            int result = imGroupService.removeAdmin(request);

            if (result > 0) {
                log.info("群管理员移除成功 - 群组ID: {}, 网易云信群组ID: {}, 管理员ID列表: {}",
                        request.getGroupId(), existGroup.getYxGroupId(), request.getUserIds());
            }

            return result;

        } catch (ServiceException e) {
            log.error("移除群管理员失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("移除群管理员异常", e);
            throw new ServiceException("移除群管理员失败: " + e.getMessage());
        }
    }

    /**
     * 解散群组
     *
     * @param groupId 群组ID
     * @param request 解散群组请求
     * @return 解散结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int dissolveGroup(Long groupId, DissolveGroupRequestDto request) {
        try {
            // 1. 获取现有群组信息
            ImGroup existGroup = imGroupService.getById(groupId);
            if (existGroup == null) {
                throw new ServiceException("群组不存在");
            }

            // 2. 检查是否有网易云信群组ID
            if (StringUtils.isEmpty(existGroup.getYxGroupId())) {
                throw new ServiceException("群组未关联网易云信，无法解散");
            }

            // 3. 构建网易云信解散群组请求
            YunxinDissolveGroupRequestDto yunxinRequest = new YunxinDissolveGroupRequestDto();

            // 设置群组类型（必填）
            yunxinRequest.setTeamType(StringUtils.isNotEmpty(existGroup.getGroupType()) ?
                    Integer.parseInt(existGroup.getGroupType()) : 1);

            // 设置操作者（群主）账号ID
            if (StringUtils.isNotEmpty(request.getOperatorId())) {
                String operatorYxAccountId = request.getOperatorId() + "_" + existGroup.getTenantId();
                yunxinRequest.setOperatorId(operatorYxAccountId);
            }

            // 设置扩展字段
            if (StringUtils.isNotEmpty(request.getExtension())) {
                yunxinRequest.setExtension(request.getExtension());
            }

            // 4. 调用网易云信API解散群组
            boolean dissolveSuccess = wyImService.dissolveYunxinGroup(existGroup.getYxGroupId(), yunxinRequest);

            if (!dissolveSuccess) {
                throw new ServiceException("解散网易云信群组失败");
            }

            // 5. 只有当网易云信API返回成功后，才更新本地数据库
            int result = imGroupService.dissolveGroup(groupId);

            if (result > 0) {
                log.info("群组解散成功 - 群组ID: {}, 网易云信群组ID: {}",
                        groupId, existGroup.getYxGroupId());
            }

            return result;

        } catch (ServiceException e) {
            log.error("解散群组失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("解散群组异常", e);
            throw new ServiceException("解散群组失败: " + e.getMessage());
        }
    }
}
