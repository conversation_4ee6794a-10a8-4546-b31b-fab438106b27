package com.ruoyi.web.controller.system;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.service.TenantSwitchService;

/**
 * 租户切换控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/tenant")
public class TenantSwitchController extends BaseController
{
    @Autowired
    private TenantSwitchService tenantSwitchService;
    
    /**
     * 切换租户身份
     * 
     * @param tenantId 租户ID
     * @return Token和租户信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:switch')")
    @PostMapping("/switch-tenant/{tenantId}")
    public AjaxResult switchTenant(@PathVariable Long tenantId)
    {
        try {
            String token = tenantSwitchService.switchTenant(tenantId);
            AjaxResult result = AjaxResult.success("切换租户成功");
            result.put("token", token);
            result.put("tenantId", tenantId);
            return result;
        } catch (Exception e) {
            return AjaxResult.error("切换租户失败：" + e.getMessage());
        }
    }
    
    /**
     * 运营人员代理租户操作
     * 
     * @param tenantId 租户ID
     * @return 代理Token和租户信息
     */
    @PreAuthorize("@ss.hasRole('operator') and @ss.hasPermi('system:tenant:impersonate')")
    @PostMapping("/impersonate/{tenantId}")
    public AjaxResult impersonateTenant(@PathVariable Long tenantId)
    {
        try {
            String token = tenantSwitchService.impersonateTenant(tenantId);
            AjaxResult result = AjaxResult.success("进入代理模式成功");
            result.put("token", token);
            result.put("tenantId", tenantId);
            result.put("isImpersonating", true);
            result.put("impersonator", SecurityUtils.getUsername());
            return result;
        } catch (Exception e) {
            return AjaxResult.error("进入代理模式失败：" + e.getMessage());
        }
    }
    
    /**
     * 退出代理模式
     * 
     * @return 恢复原始身份的Token
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:impersonate')")
    @PostMapping("/exit-impersonate")
    public AjaxResult exitImpersonate(HttpServletRequest request)
    {
        try {
            // 从请求头中获取原始Token（前端需要保存）
            String originalToken = request.getHeader("Original-Token");
            if (originalToken == null || originalToken.trim().isEmpty()) {
                return AjaxResult.error("原始Token不存在，无法退出代理模式");
            }
            
            AjaxResult result = AjaxResult.success("退出代理模式成功");
            result.put("token", originalToken);
            result.put("isImpersonating", false);
            return result;
        } catch (Exception e) {
            return AjaxResult.error("退出代理模式失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取当前用户的代理状态
     * 
     * @return 代理状态信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:view')")
    @PostMapping("/impersonate-status")
    public AjaxResult getImpersonateStatus()
    {
        try {
            AjaxResult result = AjaxResult.success();
            result.put("isImpersonating", SecurityUtils.getLoginUser().isImpersonating());
            result.put("impersonator", SecurityUtils.getLoginUser().getImpersonator());
            result.put("tenantId", SecurityUtils.getLoginUser().getTenantId());
            return result;
        } catch (Exception e) {
            return AjaxResult.error("获取代理状态失败：" + e.getMessage());
        }
    }
} 