package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.service.ImGroupMemberService;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.MemberPermissionsResponseVo;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import com.ruoyi.web.service.GroupMemberApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * IM群成员管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/im/member")
public class ImGroupMemberController extends BaseController {

    @Autowired
    private ImGroupMemberService imGroupMemberService;

    @Autowired
    private GroupMemberApiService groupMemberApiService;

    /**
     * 查询群成员列表
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(@RequestParam(required = false) Long groupId,
                             @RequestParam(required = false) String keyword,
                             @RequestParam(required = false) String role,
                             @RequestParam(required = false) String muteStatus) {
        startPage();
        try {
            List<ImGroupMember> list = imGroupMemberService.selectMemberList(groupId, keyword, role, muteStatus);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询群成员列表失败", e);
            return getDataTable(null);
        }
    }

    /**
     * 查询群成员列表（包含用户详细信息）
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @GetMapping("/listWithDetails")
    public TableDataInfo listWithUserDetails(@RequestParam Long groupId,
                                             @RequestParam(required = false) String keyword,
                                             @RequestParam(required = false) String role) {
        startPage();
        try {
            List<QwxUserTenantDetailVo> list = imGroupMemberService.selectMemberListWithUserDetails(groupId, keyword, role);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询群成员详细信息列表失败", e);
            return getDataTable(null);
        }
    }

    /**
     * 导出群成员列表
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @Log(title = "群成员管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response,
                      @RequestParam(required = false) Long groupId,
                      @RequestParam(required = false) String keyword,
                      @RequestParam(required = false) String role,
                      @RequestParam(required = false) String muteStatus) {
        try {
            List<ImGroupMember> list = imGroupMemberService.selectMemberList(groupId, keyword, role, muteStatus);
            ExcelUtil<ImGroupMember> util = new ExcelUtil<>(ImGroupMember.class);
            util.exportExcel(response, list, "群成员数据");
        } catch (Exception e) {
            logger.error("导出群成员列表失败", e);
        }
    }

    /**
     * 获取群成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @GetMapping(value = "/{groupId}/{userId}")
    public AjaxResult getInfo(@PathVariable("groupId") Long groupId,
                             @PathVariable("userId") Long userId) {
        try {
            ImGroupMember member = imGroupMemberService.getMemberInfo(groupId, userId);
            if (member == null) {
                return error("群成员不存在");
            }
            return success(member);
        } catch (Exception e) {
            logger.error("获取群成员详细信息失败", e);
            return error("获取群成员信息失败: " + e.getMessage());
        }
    }

    /**
     * 拉人入群（单个）
     */
    @PreAuthorize("@ss.hasPermi('im:member:add')")
    @Log(title = "群成员管理", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult addMember(@Validated @RequestBody AddMemberRequestDto addMemberRequest) {
        try {
            int result = groupMemberApiService.addMember(addMemberRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("添加群成员失败", e);
            return error("添加成员失败: " + e.getMessage());
        }
    }

    /**
     * 批量拉人入群
     */
    @PreAuthorize("@ss.hasPermi('im:member:batchAdd')")
    @Log(title = "群成员管理", businessType = BusinessType.INSERT)
    @PostMapping("/batch/add")
    public AjaxResult batchAddMembers(@Validated @RequestBody BatchAddMembersRequestDto batchAddRequest) {
        try {
            int result = groupMemberApiService.batchAddMembers(batchAddRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("批量添加群成员失败", e);
            return error("批量添加成员失败: " + e.getMessage());
        }
    }

    /**
     * 移除群成员（单个）
     */
    @PreAuthorize("@ss.hasPermi('im:member:remove')")
    @Log(title = "群成员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupId}/{userId}")
    public AjaxResult removeMember(@PathVariable("groupId") Long groupId,
                                  @PathVariable("userId") Long userId,
                                  @RequestParam(required = false) Long operatorId,
                                  @RequestParam(required = false) String leaveReason) {
        try {
            int result = groupMemberApiService.removeMember(groupId, userId, operatorId, leaveReason);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("移除群成员失败", e);
            return error("移除成员失败: " + e.getMessage());
        }
    }

    /**
     * 批量移除群成员
     */
    @PreAuthorize("@ss.hasPermi('im:member:remove')")
    @Log(title = "群成员管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemoveMembers(@RequestBody BatchRemoveMembersRequestDto batchRemoveRequest) {
        try {
            int result = groupMemberApiService.batchRemoveMembers(batchRemoveRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("批量移除群成员失败", e);
            return error("批量移除成员失败: " + e.getMessage());
        }
    }

    /**
     * 设置成员禁言
     */
    @PreAuthorize("@ss.hasPermi('im:member:mute')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/mute")
    public AjaxResult muteMember(@RequestBody MuteMemberRequestDto muteRequest) {
        try {
            int result = imGroupMemberService.muteMember(muteRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("设置成员禁言失败", e);
            return error("设置禁言失败: " + e.getMessage());
        }
    }

    /**
     * 取消成员禁言
     */
    @PreAuthorize("@ss.hasPermi('im:member:mute')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/unmute")
    public AjaxResult unmuteMember(@RequestBody UnmuteMemberRequestDto unmuteRequest) {
        try {
            int result = imGroupMemberService.unmuteMember(unmuteRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("取消成员禁言失败", e);
            return error("取消禁言失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置成员禁言
     */
    @PreAuthorize("@ss.hasPermi('im:member:mute')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/mute")
    public AjaxResult batchMuteMembers(@RequestBody BatchMuteMembersRequestDto batchMuteRequest) {
        try {
            int result = imGroupMemberService.batchMuteMembers(batchMuteRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("批量设置成员禁言失败", e);
            return error("批量禁言失败: " + e.getMessage());
        }
    }

    /**
     * 设置成员为管理员
     */
    @PreAuthorize("@ss.hasPermi('im:member:setAdmin')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/admin/set")
    public AjaxResult setMemberAsAdmin(@RequestBody SetMemberAdminRequestDto setAdminRequest) {
        try {
            int result = imGroupMemberService.setMemberAsAdmin(setAdminRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("设置成员为管理员失败", e);
            return error("设置管理员失败: " + e.getMessage());
        }
    }

    /**
     * 取消成员管理员身份
     */
    @PreAuthorize("@ss.hasPermi('im:member:setAdmin')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/admin/unset")
    public AjaxResult unsetMemberAsAdmin(@RequestBody SetMemberAdminRequestDto unsetAdminRequest) {
        try {
            int result = imGroupMemberService.unsetMemberAsAdmin(unsetAdminRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("取消成员管理员身份失败", e);
            return error("取消管理员失败: " + e.getMessage());
        }
    }

    /**
     * 修改成员在群昵称
     */
    @PreAuthorize("@ss.hasPermi('im:member:nickname')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/nickname")
    public AjaxResult updateMemberNickname(@RequestBody UpdateMemberNicknameRequestDto nicknameRequest) {
        try {
            int result = imGroupMemberService.updateMemberNickname(nicknameRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("修改成员昵称失败", e);
            return error("修改昵称失败: " + e.getMessage());
        }
    }

    /**
     * 获取成员在群中的权限
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @GetMapping("/{groupId}/{userId}/permissions")
    public AjaxResult getMemberPermissions(@PathVariable("groupId") Long groupId,
                                          @PathVariable("userId") Long userId) {
        try {
            MemberPermissionsResponseVo permissions = imGroupMemberService.getMemberPermissions(groupId, userId);
            if (permissions == null) {
                return error("群成员不存在");
            }
            return success(permissions);
        } catch (Exception e) {
            logger.error("获取成员权限失败", e);
            return error("获取成员权限失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新成员权限
     */
    @PreAuthorize("@ss.hasPermi('im:member:setAdmin')")
    @Log(title = "群成员管理", businessType = BusinessType.UPDATE)
    @PutMapping("/batch/permissions")
    public AjaxResult batchUpdatePermissions(@RequestBody BatchUpdatePermissionsRequestDto batchPermissionRequest) {
        try {
            int result = imGroupMemberService.batchUpdatePermissions(batchPermissionRequest);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("批量更新成员权限失败", e);
            return error("批量权限更新失败: " + e.getMessage());
        }
    }

    /**
     * 查询成员加入群组的历史记录
     */
    @PreAuthorize("@ss.hasPermi('im:member:list')")
    @GetMapping("/{groupId}/{userId}/history")
    public TableDataInfo getMemberHistory(@PathVariable("groupId") Long groupId,
                                         @PathVariable("userId") Long userId) {
        startPage();
        try {
            List<ImGroupMember> list = imGroupMemberService.getMemberHistory(groupId, userId);
            return getDataTable(list);
        } catch (Exception e) {
            logger.error("查询成员历史记录失败", e);
            return getDataTable(null);
        }
    }
} 