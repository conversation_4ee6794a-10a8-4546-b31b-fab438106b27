package com.ruoyi.web.controller.system;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import com.ruoyi.app.service.QwxUserTenantService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 租户用户管理Controller
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
@RestController
@RequestMapping("/system/tenantUser")
public class QwxTenantUserController extends BaseController
{
    @Resource
    private QwxUserTenantService qwxUserTenantService;

    /**
     * 查询租户内用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenantUser:list')")
    @GetMapping("/list")
    public TableDataInfo list(QwxUserTenant qwxUserTenant)
    {
        startPage();
        List<QwxUserTenantDetailVo> list = qwxUserTenantService.selectQwxUserTenantDetailList(qwxUserTenant);
        return getDataTable(list);
    }

    /**
     * 查询租户下用户详细信息列表（包含联系方式）
     * 用于群主选择等需要完整用户信息的场景
     */
    @PreAuthorize("@ss.hasPermi('system:tenantUser:list')")
    @GetMapping("/listWithDetails")
    public AjaxResult listWithDetails(@RequestParam Long tenantId)
    {
        List<QwxUserTenantDetailVo> list = qwxUserTenantService.selectTenantUserDetailList(tenantId);
        return success(list);
    }

    /**
     * 导出租户内用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenantUser:export')")
    @Log(title = "租户用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QwxUserTenant qwxUserTenant)
    {
        List<QwxUserTenantDetailVo> list = qwxUserTenantService.selectQwxUserTenantDetailList(qwxUserTenant);
        ExcelUtil<QwxUserTenantDetailVo> util = new ExcelUtil<QwxUserTenantDetailVo>(QwxUserTenantDetailVo.class);
        util.exportExcel(response, list, "租户用户数据");
    }

    /**
     * 获取租户内用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenantUser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(qwxUserTenantService.getById(id));
    }

    /**
     * 修改租户内用户状态（启用/禁用）
     */
    @PreAuthorize("@ss.hasPermi('system:tenantUser:edit')")
    @Log(title = "租户用户", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody QwxUserTenant qwxUserTenant)
    {
        // 只允许修改状态字段
        QwxUserTenant updateUser = new QwxUserTenant();
        updateUser.setId(qwxUserTenant.getId());
        updateUser.setStatus(qwxUserTenant.getStatus());
        
        return toAjax(qwxUserTenantService.updateById(updateUser));
    }
} 