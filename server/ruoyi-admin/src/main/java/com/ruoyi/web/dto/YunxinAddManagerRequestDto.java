package com.ruoyi.web.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 网易云信添加管理员请求DTO
 * 根据网易云信API文档定义
 * 
 * <AUTHOR>
 */
@Data
public class YunxinAddManagerRequestDto {

    /**
     * 群组类型（必填）
     * 1：高级群
     * 2：超大群
     */
    @NotNull(message = "群组类型不能为空")
    @JSONField(name = "team_type")
    private Integer teamType;

    /**
     * 操作者（群主）账号 ID（可选）
     */
    @JSONField(name = "operator_id")
    private String operatorId;

    /**
     * 需要添加为群管理员的群成员列表（必填）
     * 单个元素为群成员的账号 ID，一次最多添加 10 个管理员
     */
    @NotNull(message = "管理员列表不能为空")
    @JSONField(name = "managers")
    private List<String> managers;

    /**
     * 自定义扩展字段（可选）
     * 即自定义的通知字段，JSON 格式，不会持久化
     * 长度上限 512 个字符
     * 对应客户端 SDK 通知消息附件 V2NIMMessageNotificationAttachment 的 serverExtension 字段
     * 该字段仅针对高级群，对超大群无效
     */
    @JSONField(name = "extension")
    private String extension;
} 