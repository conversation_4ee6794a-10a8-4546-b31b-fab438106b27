package com.ruoyi.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.service.ImConfigService;
import com.ruoyi.app.domain.ImConfig;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.ConfigResponseVo;
import com.ruoyi.app.model.vo.NeteaseConnectionTestResponseVo;
import com.ruoyi.app.model.vo.SystemConfigResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;

/**
 * IM配置管理Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/im/config")
public class ImConfigController extends BaseController {

    @Autowired
    private ImConfigService imConfigService;

    /**
     * 查询IM配置列表
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping("/list")
    public TableDataInfo list(ConfigQueryRequestDto queryRequest) {
        startPage();
        List<ImConfig> list = imConfigService.selectConfigList(queryRequest);
        return getDataTable(list);
    }

    /**
     * 导出IM配置列表
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @Log(title = "IM配置管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConfigQueryRequestDto queryRequest) {
        List<ImConfig> list = imConfigService.selectConfigList(queryRequest);
        ExcelUtil<ImConfig> util = new ExcelUtil<>(ImConfig.class);
        util.exportExcel(response, list, "IM配置数据");
    }

    /**
     * 获取IM配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId) {
        ImConfig config = imConfigService.getById(configId);
        if (config == null) {
            return error("配置不存在");
        }
        return success(config);
    }

    /**
     * 根据配置键获取配置值
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping(value = "/key/{configKey}")
    public AjaxResult getConfigByKey(@PathVariable("configKey") String configKey) {
        try {
            ConfigResponseVo configVo = imConfigService.getConfigByKey(configKey);
            if (configVo == null) {
                return error("配置不存在");
            }
            return success(configVo);
        } catch (Exception e) {
            return error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取网易云信AppKey配置
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping("/netease/appkey")
    public AjaxResult getNeteaseAppKey() {
        try {
            String appKey = imConfigService.getNeteaseAppKey();
            return success(appKey);
        } catch (Exception e) {
            return error("获取网易云信AppKey失败: " + e.getMessage());
        }
    }

    /**
     * 获取网易云信AppSecret配置（脱敏）
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping("/netease/appsecret")
    public AjaxResult getNeteaseAppSecret() {
        try {
            String appSecret = imConfigService.getNeteaseAppSecretMasked();
            return success(appSecret);
        } catch (Exception e) {
            return error("获取网易云信AppSecret失败: " + e.getMessage());
        }
    }

    /**
     * 新增IM配置
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CreateConfigRequestDto createConfigRequest) {
        try {
            int result = imConfigService.insertConfig(createConfigRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("新增IM配置失败: " + e.getMessage());
        }
    }

    /**
     * 修改IM配置
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody UpdateConfigRequestDto updateConfigRequest) {
        try {
            int result = imConfigService.updateConfig(updateConfigRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("修改IM配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新网易云信AppKey
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.UPDATE)
    @PutMapping("/netease/appkey")
    public AjaxResult updateNeteaseAppKey(@Validated @RequestBody UpdateNeteaseAppKeyRequestDto appKeyRequest) {
        try {
            int result = imConfigService.updateNeteaseAppKey(appKeyRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("更新网易云信AppKey失败: " + e.getMessage());
        }
    }

    /**
     * 更新网易云信AppSecret
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.UPDATE)
    @PutMapping("/netease/appsecret")
    public AjaxResult updateNeteaseAppSecret(@Validated @RequestBody UpdateNeteaseAppSecretRequestDto appSecretRequest) {
        try {
            int result = imConfigService.updateNeteaseAppSecret(appSecretRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("更新网易云信AppSecret失败: " + e.getMessage());
        }
    }

    /**
     * 删除IM配置
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds) {
        try {
            int result = imConfigService.deleteConfigByIds(configIds);
            return toAjax(result);
        } catch (Exception e) {
            return error("删除IM配置失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用配置开关
     */
    @PreAuthorize("@ss.hasPermi('im:config:switch')")
    @Log(title = "IM配置管理", businessType = BusinessType.UPDATE)
    @PutMapping("/switch")
    public AjaxResult switchConfig(@Validated @RequestBody ConfigSwitchRequestDto switchRequest) {
        try {
            int result = imConfigService.switchConfig(switchRequest);
            return toAjax(result);
        } catch (Exception e) {
            return error("配置状态切换失败: " + e.getMessage());
        }
    }

    /**
     * 测试网易云信连接
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @PostMapping("/netease/test")
    public AjaxResult testNeteaseConnection() {
        try {
            NeteaseConnectionTestResponseVo testResult = imConfigService.testNeteaseConnection();
            return success(testResult);
        } catch (Exception e) {
            return error("网易云信连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 刷新配置缓存
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.CLEAN)
    @DeleteMapping("/refreshCache")
    public AjaxResult refreshCache() {
        try {
            imConfigService.refreshCache();
            return success("配置缓存刷新成功");
        } catch (Exception e) {
            return error("配置缓存刷新失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统配置信息
     */
    @PreAuthorize("@ss.hasPermi('im:config:appkey')")
    @GetMapping("/system")
    public AjaxResult getSystemConfig() {
        try {
            SystemConfigResponseVo systemConfig = imConfigService.getSystemConfig();
            return success(systemConfig);
        } catch (Exception e) {
            return error("获取系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 导入配置文件
     */
    @PreAuthorize("@ss.hasPermi('im:config:edit')")
    @Log(title = "IM配置管理", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importConfig(@Validated @RequestBody ConfigImportRequestDto importRequest) {
        try {
            String result = imConfigService.importConfig(importRequest);
            return success(result);
        } catch (Exception e) {
            return error("配置导入失败: " + e.getMessage());
        }
    }
} 