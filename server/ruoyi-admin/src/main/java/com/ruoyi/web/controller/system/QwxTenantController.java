package com.ruoyi.web.controller.system;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.app.domain.QwxTenant;
import com.ruoyi.app.service.QwxTenantService;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.web.service.TenantManagementService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 租户Controller
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
@RestController
@RequestMapping("/system/tenant")
public class QwxTenantController extends BaseController
{
    @Resource
    private QwxTenantService qwxTenantService;
    
    @Resource
    private TenantManagementService tenantManagementService;

    /**
     * 查询租户列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:list')")
    @GetMapping("/list")
    public TableDataInfo list(QwxTenant qwxTenant)
    {
        startPage();
        List<QwxTenant> list = tenantManagementService.selectTenantList(qwxTenant);
        return getDataTable(list);
    }

    /**
     * 导出租户列表
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:export')")
    @Log(title = "租户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QwxTenant qwxTenant)
    {
        List<QwxTenant> list = tenantManagementService.selectTenantList(qwxTenant);
        ExcelUtil<QwxTenant> util = new ExcelUtil<QwxTenant>(QwxTenant.class);
        util.exportExcel(response, list, "租户数据");
    }

    /**
     * 获取租户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:query')")
    @GetMapping(value = "/{tenantId}")
    public AjaxResult getInfo(@PathVariable("tenantId") Long tenantId)
    {
        return success(tenantManagementService.selectTenantById(tenantId));
    }

    /**
     * 新增租户
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:add')")
    @Log(title = "租户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QwxTenant qwxTenant)
    {
        LoginUser currentUser = SecurityUtils.getLoginUser();
        qwxTenant.setCreateUserId(currentUser.getUserId());
        return toAjax(tenantManagementService.insertTenant(qwxTenant));
    }

    /**
     * 修改租户
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:edit')")
    @Log(title = "租户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QwxTenant qwxTenant)
    {
        return toAjax(tenantManagementService.updateTenant(qwxTenant));
    }

    /**
     * 删除租户
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:remove')")
    @Log(title = "租户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tenantIds}")
    public AjaxResult remove(@PathVariable Long[] tenantIds)
    {
        return toAjax(tenantManagementService.deleteTenantByIds(tenantIds));
    }
    
    /**
     * 获取当前运营人员创建的租户列表（用于用户关联租户下拉选择）
     */
    @PreAuthorize("@ss.hasPermi('system:tenant:list')")
    @GetMapping("/operatorTenantList")
    public AjaxResult getOperatorTenantList()
    {
        QwxTenant queryTenant = new QwxTenant();
        // 只获取当前运营人员创建的租户
        queryTenant.setCreateUserId(SecurityUtils.getUserId());
        queryTenant.setStatus("0"); // 只获取正常状态的租户
        List<QwxTenant> list = tenantManagementService.selectTenantList(queryTenant);
        return success(list);
    }
}
