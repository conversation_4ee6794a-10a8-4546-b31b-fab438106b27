package com.ruoyi.web.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ruoyi.app.domain.QwxTenant;
import com.ruoyi.app.mapper.QwxTenantMapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 租户管理服务
 * 
 * <AUTHOR>
 */
@Service
public class TenantManagementService
{
    @Autowired
    private QwxTenantMapper tenantMapper;
    
    /**
     * 查询租户列表（支持数据权限过滤）
     * 
     * @param tenant 租户查询条件
     * @return 租户列表
     */
    @DataScope(deptAlias = "t")
    public List<QwxTenant> selectTenantList(QwxTenant tenant)
    {
        return tenantMapper.selectQwxTenantList(tenant);
    }
    
    /**
     * 根据ID查询租户（支持数据权限验证）
     * 
     * @param tenantId 租户ID
     * @return 租户信息
     */
    @DataScope(deptAlias = "t")
    public QwxTenant selectTenantById(Long tenantId)
    {
        // 创建查询条件，应用数据权限过滤
        QwxTenant tenant = new QwxTenant();
        tenant.setTenantId(tenantId);
        List<QwxTenant> list = tenantMapper.selectQwxTenantList(tenant);
        return list.isEmpty() ? null : list.get(0);
    }
    
    /**
     * 新增租户
     * 
     * @param tenant 租户信息
     * @return 结果
     */
    public int insertTenant(QwxTenant tenant)
    {
        // 设置创建用户ID
        if (tenant.getCreateUserId() == null) {
            tenant.setCreateUserId(SecurityUtils.getUserId());
        }
        return tenantMapper.insert(tenant);
    }
    
    /**
     * 修改租户
     * 
     * @param tenant 租户信息
     * @return 结果
     */
    @DataScope(deptAlias = "t")
    public int updateTenant(QwxTenant tenant)
    {
        // 先验证当前用户是否有权限修改该租户
        QwxTenant existingTenant = selectTenantById(tenant.getTenantId());
        if (existingTenant == null) {
            return 0; // 租户不存在或无权限访问
        }
        return tenantMapper.updateById(tenant);
    }
    
    /**
     * 删除租户
     * 
     * @param tenantId 租户ID
     * @return 结果
     */
    @DataScope(deptAlias = "t")
    public int deleteTenant(Long tenantId)
    {
        // 先验证当前用户是否有权限删除该租户
        QwxTenant existingTenant = selectTenantById(tenantId);
        if (existingTenant == null) {
            return 0; // 租户不存在或无权限访问
        }
        return tenantMapper.deleteById(tenantId);
    }
    
    /**
     * 批量删除租户
     * 
     * @param tenantIds 租户ID数组
     * @return 结果
     */
    public int deleteTenantByIds(Long[] tenantIds)
    {
        int result = 0;
        for (Long tenantId : tenantIds) {
            result += deleteTenant(tenantId);
        }
        return result;
    }
} 