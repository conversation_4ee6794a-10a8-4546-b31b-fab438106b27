# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# 短信配置
sms:
  # 是否启用短信功能
  enabled: true
  # 短信服务商类型（aliyun/tencent/default）
  type: default
  # 短信签名
  signName: 微企信
  # 短信模板ID
  templateId: SMS_123456789
  # 短信服务商访问密钥ID（根据实际配置）
  accessKeyId:
  # 短信服务商访问密钥（根据实际配置）
  accessKeySecret:
  # 短信发送超时时间（秒）
  timeout: 10

# 邮件配置
email:
  # 是否启用邮件功能
  enabled: true
  # 邮件服务器地址
  host: smtp.qq.com
  # 邮件服务器端口
  port: 465
  # 是否使用SSL
  ssl: true
  # 发件人地址
  from: 
  # 邮箱用户名
  username: 
  # 邮箱密码（授权码）
  password: 
  # 邮件主题
  subject: 微企信验证码
  # 邮件内容模板
  template: "<div style='background-color:#f5f5f5;padding:20px;font-family:Arial,sans-serif'><div style='max-width:600px;margin:0 auto;background-color:white;border-radius:5px;padding:20px;box-shadow:0px 2px 5px rgba(0,0,0,0.1)'><h2 style='color:#333;text-align:center'>微企信验证码</h2><div style='padding:20px;background-color:#f9f9f9;border-radius:5px;text-align:center;margin:20px 0'><h3 style='color:#333;font-size:20px;margin:0'>您的验证码是: <span style='color:#2b73af;font-weight:bold;letter-spacing:2px'>%s</span></h3><p style='color:#666;margin-top:15px'>验证码有效期为 %d 分钟</p></div><p style='color:#666;font-size:13px;text-align:center;margin-top:20px'>此邮件由系统自动发送，请勿回复</p></div></div>"
  # 邮件发送超时（秒）
  timeout: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 7ghGygfu2hj10fd1fdgh9haws1
  # 令牌有效期（默认30分钟）
  expireTime: 30

# APP token配置
app-token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 45fDha1g7JkMn0OpQ8rSxT9uV6wXyZ2A
  # 令牌有效期（30天，单位分钟）
  expireTime: 43200
  # 是否允许多端同时登录
  multiLogin: true

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 网易云信配置
yunxin:
  app-key: 1383bae1ef7a464bfb3025f2df215d0c
  app-secret: 72d08124bd17
