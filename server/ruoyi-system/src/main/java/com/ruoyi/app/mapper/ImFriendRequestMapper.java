package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImFriendRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 好友申请记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ImFriendRequestMapper extends BaseMapper<ImFriendRequest> {

    /**
     * 根据接收者用户ID和租户ID查询好友申请列表
     * 
     * @param tenantId 租户ID
     * @param receiverUserId 接收者用户ID
     * @param status 申请状态（可选）
     * @return 好友申请列表
     */
    List<ImFriendRequest> selectByReceiverUserId(@Param("tenantId") Long tenantId, 
                                                 @Param("receiverUserId") Long receiverUserId, 
                                                 @Param("status") String status);

    /**
     * 根据发送者和接收者查询好友申请记录
     * 
     * @param tenantId 租户ID
     * @param senderUserId 发送者用户ID
     * @param receiverUserId 接收者用户ID
     * @return 好友申请记录
     */
    ImFriendRequest selectBySenderAndReceiver(@Param("tenantId") Long tenantId, 
                                              @Param("senderUserId") Long senderUserId, 
                                              @Param("receiverUserId") Long receiverUserId);
}
