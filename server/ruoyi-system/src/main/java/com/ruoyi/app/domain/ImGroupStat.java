package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 群组统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_group_stat")
public class ImGroupStat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 统计ID
     */
    @TableId(value = "stat_id", type = IdType.AUTO)
    private Long statId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 成员数量
     */
    private Integer memberCount;

    /**
     * 在线人数
     */
    private Integer onlineCount;

    /**
     * 当日消息数
     */
    private Integer msgCount;

    /**
     * 活跃成员数（当日发言）
     */
    private Integer activeMemberCount;

    /**
     * 新增成员数
     */
    private Integer joinCount;

    /**
     * 退出成员数
     */
    private Integer leaveCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 