package com.ruoyi.app.model.vo;

import com.ruoyi.app.domain.QwxUserTenant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户加入租户信息列表
 *
 * <AUTHOR>
 * @date 2025/5/13 下午5:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserJoinTenantInfoList extends QwxUserTenant {
    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称", example = "阿里巴巴")
    private String tenantName;

    /**
     * 租户编码，唯一标识
     */
    @ApiModelProperty(value = "租户编码", example = "ALIBABA001")
    private String tenantCode;

    /**
     * 企业logo
     */
    @ApiModelProperty(value = "企业logo URL", example = "https://example.com/logo.png")
    private String logo;
}
