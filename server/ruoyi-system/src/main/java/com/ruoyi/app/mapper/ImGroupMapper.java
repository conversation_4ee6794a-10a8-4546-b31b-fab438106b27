package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImGroup;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 群组表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface ImGroupMapper extends BaseMapper<ImGroup> {

    /**
     * 更新群组成员数量
     * 根据群组ID统计正常状态的成员数量并更新到群组表
     *
     * @param groupId 群组ID
     * @return 更新结果
     */
    int updateGroupMemberCount(@Param("groupId") Long groupId);
}
