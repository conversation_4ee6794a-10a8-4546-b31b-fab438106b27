package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户信息更新DTO
 * <AUTHOR>
 * @date 2025/5/10 下午11:32
 */
@Data
@ApiModel("用户信息更新DTO")
public class UpdataUserInfoDto {

    /**
     * 用户昵称
     */
    @ApiModelProperty("用户昵称")
    private String nickName;

    /**
     * 用户性别（0男 1女 2未知）
     */
    @ApiModelProperty(value = "用户性别（0男 1女 2未知）", allowableValues = "0,1,2")
    private String sex;

    /**
     * 头像地址
     */
    @ApiModelProperty("头像地址")
    private String avatar;

    /**
     * 个性签名
     */
    @ApiModelProperty("个性签名")
    private String signature;
}
