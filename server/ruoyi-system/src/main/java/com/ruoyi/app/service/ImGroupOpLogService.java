package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImGroupOpLog;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组操作日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImGroupOpLogService extends IService<ImGroupOpLog> {

    /**
     * 记录群组操作日志
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param operatorId 操作人ID
     * @param targetUserId 目标用户ID
     * @param operationType 操作类型
     * @param operationDesc 操作描述
     * @param beforeData 操作前数据
     * @param afterData 操作后数据
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 是否记录成功
     */
    boolean logGroupOperation(Long tenantId, Long groupId, Long operatorId, 
                              Long targetUserId, String operationType, String operationDesc,
                              String beforeData, String afterData, 
                              String clientIp, String userAgent);

    /**
     * 根据群组ID查询操作日志
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    List<ImGroupOpLog> getLogsByGroupId(Long tenantId, Long groupId, Date startTime, Date endTime);

    /**
     * 根据操作人ID查询操作日志
     * 
     * @param tenantId 租户ID
     * @param operatorId 操作人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    List<ImGroupOpLog> getLogsByOperatorId(Long tenantId, Long operatorId, Date startTime, Date endTime);
} 