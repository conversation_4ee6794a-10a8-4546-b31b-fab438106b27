package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImGroupOpLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组操作日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImGroupOpLogMapper extends BaseMapper<ImGroupOpLog> {

    /**
     * 根据群组ID查询操作日志
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    List<ImGroupOpLog> selectByGroupId(@Param("tenantId") Long tenantId, 
                                       @Param("groupId") Long groupId,
                                       @Param("startTime") Date startTime,
                                       @Param("endTime") Date endTime);

    /**
     * 根据操作人ID查询操作日志
     * 
     * @param tenantId 租户ID
     * @param operatorId 操作人ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 操作日志列表
     */
    List<ImGroupOpLog> selectByOperatorId(@Param("tenantId") Long tenantId, 
                                          @Param("operatorId") Long operatorId,
                                          @Param("startTime") Date startTime,
                                          @Param("endTime") Date endTime);
} 