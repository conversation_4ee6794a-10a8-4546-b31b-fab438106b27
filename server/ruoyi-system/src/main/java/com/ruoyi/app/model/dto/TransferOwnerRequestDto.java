package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 转让群主请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "转让群主请求参数", description = "用于转让群主权限的请求参数")
public class TransferOwnerRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要转让群主权限的群组ID")
    private Long groupId;

    /**
     * 新群主ID
     */
    @NotNull(message = "新群主ID不能为空")
    @ApiModelProperty(value = "新群主ID", required = true, example = "2", notes = "接收群主权限的用户ID")
    private Long newOwnerId;

    /**
     * 是否退出群组（0否 1是）
     */
    @ApiModelProperty(value = "是否退出群组", example = "0", notes = "转让后原群主是否退出群组，0否 1是", allowableValues = "0,1")
    private String leaveGroup;

    /**
     * 原群主ID
     */
    @ApiModelProperty(value = "原群主ID", example = "1", notes = "转让前的群主ID")
    private Long oldOwnerId;
} 