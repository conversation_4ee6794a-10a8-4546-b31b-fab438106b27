package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 群组成员表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_group_member")
public class ImGroupMember implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邀请人ID
     */
    private Long inviteUserId;

    /**
     * 加入来源（0直接加入 1邀请加入 2扫码加入 3审批加入）
     */
    private String joinSource;

    /**
     * 群内昵称
     */
    private String nickname;

    /**
     * 角色（0普通成员 1管理员 2群主）
     */
    private String role;

    /**
     * 加入时间
     */
    private Date joinTime;

    /**
     * 离开时间
     */
    private Date leaveTime;

    /**
     * 离开原因
     */
    private String leaveReason;

    /**
     * 禁言结束时间
     */
    private Date muteEndTime;

    /**
     * 状态（0正常 1退出）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
