package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImCloudRequest;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 云信接口幂等记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImCloudRequestMapper extends BaseMapper<ImCloudRequest> {

    /**
     * 根据请求UUID查询记录
     * 
     * @param requestUuid 请求UUID
     * @return 请求记录
     */
    ImCloudRequest selectByRequestUuid(@Param("requestUuid") String requestUuid);

    /**
     * 根据租户ID和接口名称查询记录
     * 
     * @param tenantId 租户ID
     * @param apiName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 请求记录列表
     */
    List<ImCloudRequest> selectByTenantIdAndApi(@Param("tenantId") Long tenantId, 
                                                @Param("apiName") String apiName,
                                                @Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime);

    /**
     * 查询失败的请求记录
     * 
     * @param tenantId 租户ID
     * @param maxRetryCount 最大重试次数
     * @return 失败请求记录列表
     */
    List<ImCloudRequest> selectFailedRequests(@Param("tenantId") Long tenantId, 
                                              @Param("maxRetryCount") Integer maxRetryCount);

    /**
     * 清理过期记录
     * 
     * @param expireTime 过期时间
     * @return 清理记录数
     */
    int deleteExpiredRecords(@Param("expireTime") Date expireTime);
} 