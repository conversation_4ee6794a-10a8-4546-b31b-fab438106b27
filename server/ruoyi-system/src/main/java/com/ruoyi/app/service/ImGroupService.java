package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImGroup;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.GroupStatisticsResponseVo;

import java.util.List;

/**
 * <p>
 * 群组表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface ImGroupService extends IService<ImGroup> {

    /**
     * 查询群组列表
     *
     * @param groupName 群组名称
     * @param groupType 群类型
     * @param status    状态
     * @param tenantId  租户ID
     * @return 群组列表
     */
    List<ImGroup> selectGroupList(String groupName, String groupType, String status, Long tenantId);

    /**
     * 批量查询群组信息
     *
     * @param groupIds 群组ID列表
     * @return 群组列表
     */
    List<ImGroup> selectGroupByIds(List<Long> groupIds);

    /**
     * 创建群组
     *
     * @param request 创建群组请求
     * @return 创建结果
     */
    int createGroup(CreateGroupRequestDto request);

    /**
     * 更新群组信息
     *
     * @param request 更新群组请求
     * @return 更新结果
     */
    int updateGroup(UpdateGroupRequestDto request);

    /**
     * 转让群主
     *
     * @param request 转让群主请求
     * @return 转让结果
     */
    int transferOwner(TransferOwnerRequestDto request);

    /**
     * 添加群管理员
     *
     * @param request 添加管理员请求
     * @return 添加结果
     */
    int addAdmin(AdminManageRequestDto request);

    /**
     * 移除群管理员
     *
     * @param request 移除管理员请求
     * @return 移除结果
     */
    int removeAdmin(AdminManageRequestDto request);

    /**
     * 解散群组
     *
     * @param groupId 群组ID
     * @return 解散结果
     */
    int dissolveGroup(Long groupId);



    /**
     * 查询高级群在线成员列表
     *
     * @param groupId 群组ID
     * @return 在线成员列表
     */
    List<ImGroupMember> getOnlineMembers(Long groupId);

    /**
     * 批量查询高级群在线人数
     *
     * @param groupIds 群组ID列表
     * @return 在线人数统计
     */
    List<Object> getOnlineMemberCount(List<Long> groupIds);

    /**
     * 分页查询群成员列表
     *
     * @param groupId    群组ID
     * @param keyword    关键字
     * @param role       角色
     * @param muteStatus 禁言状态
     * @return 群成员列表
     */
    List<ImGroupMember> selectGroupMembers(Long groupId, String keyword, String role, String muteStatus);

    /**
     * 获取群组统计信息
     *
     * @param groupId 群组ID
     * @return 统计信息
     */
    GroupStatisticsResponseVo getGroupStatistics(Long groupId);
} 