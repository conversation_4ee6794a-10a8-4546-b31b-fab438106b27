package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImCloudRequest;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 云信接口幂等记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImCloudRequestService extends IService<ImCloudRequest> {

    /**
     * 开始记录接口请求
     * 
     * @param tenantId 租户ID
     * @param requestUuid 请求UUID
     * @param apiName 接口名称
     * @param requestUrl 请求URL
     * @param requestMethod 请求方法
     * @param requestHeaders 请求头
     * @param requestBody 请求体
     * @return 请求记录ID
     */
    Long startRequest(Long tenantId, String requestUuid, String apiName, 
                      String requestUrl, String requestMethod, 
                      String requestHeaders, String requestBody);

    /**
     * 完成请求记录
     * 
     * @param requestUuid 请求UUID
     * @param responseCode 响应状态码
     * @param responseBody 响应体
     * @param costTime 耗时
     * @param status 状态
     * @param errorMsg 错误信息
     * @return 是否更新成功
     */
    boolean finishRequest(String requestUuid, Integer responseCode, String responseBody, 
                          Integer costTime, String status, String errorMsg);

    /**
     * 根据请求UUID查询记录
     * 
     * @param requestUuid 请求UUID
     * @return 请求记录
     */
    ImCloudRequest getByRequestUuid(String requestUuid);

    /**
     * 检查请求是否已存在
     * 
     * @param requestUuid 请求UUID
     * @return 是否存在
     */
    boolean isRequestExists(String requestUuid);

    /**
     * 根据租户ID和接口名称查询记录
     * 
     * @param tenantId 租户ID
     * @param apiName 接口名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 请求记录列表
     */
    List<ImCloudRequest> getRequestsByTenantAndApi(Long tenantId, String apiName, 
                                                   Date startTime, Date endTime);

    /**
     * 查询失败的请求记录
     * 
     * @param tenantId 租户ID
     * @param maxRetryCount 最大重试次数
     * @return 失败请求记录列表
     */
    List<ImCloudRequest> getFailedRequests(Long tenantId, Integer maxRetryCount);

    /**
     * 重试失败的请求
     * 
     * @param requestUuid 请求UUID
     * @return 是否重试成功
     */
    boolean retryRequest(String requestUuid);

    /**
     * 清理过期记录
     * 
     * @param expireTime 过期时间
     * @return 清理记录数
     */
    int cleanExpiredRecords(Date expireTime);

    /**
     * 生成请求UUID
     * 
     * @param tenantId 租户ID
     * @param apiName 接口名称
     * @param requestParams 请求参数
     * @return 请求UUID
     */
    String generateRequestUuid(Long tenantId, String apiName, String requestParams);
} 