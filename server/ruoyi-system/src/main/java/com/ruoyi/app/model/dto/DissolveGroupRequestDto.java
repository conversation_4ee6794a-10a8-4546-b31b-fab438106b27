package com.ruoyi.app.model.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 解散群组请求DTO
 *
 * <AUTHOR>
 */
@Data
public class DissolveGroupRequestDto {
    
    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    private Long groupId;
    
    /**
     * 操作者（群主）账号 ID
     */
    private String operatorId;
    
    /**
     * 解散的群组类型。1：高级群。2：超大群。
     */
    @NotNull(message = "群组类型不能为空")
    private Integer teamType;
    
    /**
     * 自定义扩展字段，即自定义的通知字段，JSON 格式，不会持久化。
     * 长度上限 512 位字符。对应客户端 SDK 通知消息附件 V2NIMMessageNotificationAttachment 的 serverExtension 字段。
     * 该字段仅针对高级群，对超大群无效。
     */
    private String extension;
} 