package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.mapper.QwxTenantConfigMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.app.domain.QwxTenantConfig;
import com.ruoyi.app.service.QwxTenantConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Arrays;
import java.util.List;

/**
 * 租户配置Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class QwxTenantConfigServiceImpl extends ServiceImpl<QwxTenantConfigMapper, QwxTenantConfig> implements QwxTenantConfigService {

    /**
     * 查询租户配置列表
     *
     * @param tenantConfig 租户配置
     * @return 租户配置
     */
    @Override
    public List<QwxTenantConfig> selectTenantConfigList(QwxTenantConfig tenantConfig) {
        LambdaQueryWrapper<QwxTenantConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        if (tenantConfig.getTenantId() != null) {
            queryWrapper.eq(QwxTenantConfig::getTenantId, tenantConfig.getTenantId());
        }
        
        if (StringUtils.isNotEmpty(tenantConfig.getConfigKey())) {
            queryWrapper.like(QwxTenantConfig::getConfigKey, tenantConfig.getConfigKey());
        }
        
        if (StringUtils.isNotEmpty(tenantConfig.getConfigName())) {
            queryWrapper.like(QwxTenantConfig::getConfigName, tenantConfig.getConfigName());
        }
        
        if (StringUtils.isNotEmpty(tenantConfig.getConfigType())) {
            queryWrapper.eq(QwxTenantConfig::getConfigType, tenantConfig.getConfigType());
        }
        
        if (StringUtils.isNotEmpty(tenantConfig.getStatus())) {
            queryWrapper.eq(QwxTenantConfig::getStatus, tenantConfig.getStatus());
        }
        
        queryWrapper.orderByAsc(QwxTenantConfig::getOrderNum);
        
        return list(queryWrapper);
    }

    /**
     * 根据租户ID和配置键获取配置值
     *
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    public String getConfigValue(Long tenantId, String configKey) {
        LambdaQueryWrapper<QwxTenantConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QwxTenantConfig::getTenantId, tenantId)
                  .eq(QwxTenantConfig::getConfigKey, configKey);
        
        QwxTenantConfig config = getOne(queryWrapper);
        return config != null ? config.getConfigValue() : null;
    }

    /**
     * 根据租户ID获取所有配置
     *
     * @param tenantId 租户ID
     * @return 配置列表
     */
    @Override
    public List<QwxTenantConfig> getConfigByTenantId(Long tenantId) {
        LambdaQueryWrapper<QwxTenantConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QwxTenantConfig::getTenantId, tenantId)
                  .orderByAsc(QwxTenantConfig::getOrderNum);
        
        return list(queryWrapper);
    }

    /**
     * 新增租户配置
     *
     * @param tenantConfig 租户配置
     * @return 结果
     */
    @Override
    public boolean insertTenantConfig(QwxTenantConfig tenantConfig) {
        return save(tenantConfig);
    }

    /**
     * 修改租户配置
     *
     * @param tenantConfig 租户配置
     * @return 结果
     */
    @Override
    public boolean updateTenantConfig(QwxTenantConfig tenantConfig) {
        return updateById(tenantConfig);
    }
    
    /**
     * 批量更新租户配置
     *
     * @param tenantId 租户ID
     * @param configs 配置列表
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatch(Long tenantId, List<QwxTenantConfig> configs) {
        if (configs == null || configs.isEmpty()) {
            return false;
        }
        
        for (QwxTenantConfig config : configs) {
            if (config.getTenantId() == null) {
                config.setTenantId(tenantId);
            }
            
            LambdaQueryWrapper<QwxTenantConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(QwxTenantConfig::getTenantId, config.getTenantId())
                      .eq(QwxTenantConfig::getConfigKey, config.getConfigKey());
            
            QwxTenantConfig existingConfig = getOne(queryWrapper);
            if (existingConfig != null) {
                config.setConfigId(existingConfig.getConfigId());
                updateById(config);
            } else {
                save(config);
            }
        }
        
        return true;
    }

    /**
     * 批量删除租户配置
     *
     * @param configIds 需要删除的租户配置ID
     * @return 结果
     */
    @Override
    public boolean deleteTenantConfigByIds(Long[] configIds) {
        return removeByIds(Arrays.asList(configIds));
    }
    
    /**
     * 重置租户配置为默认值
     *
     * @param tenantId 租户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefault(Long tenantId) {
        // 查询默认配置
        LambdaQueryWrapper<QwxTenantConfig> defaultConfigWrapper = new LambdaQueryWrapper<>();
        defaultConfigWrapper.eq(QwxTenantConfig::getIsDefault, "Y");
        List<QwxTenantConfig> defaultConfigs = list(defaultConfigWrapper);
        
        if (defaultConfigs.isEmpty()) {
            return false;
        }
        
        // 删除租户现有配置
        LambdaQueryWrapper<QwxTenantConfig> tenantConfigWrapper = new LambdaQueryWrapper<>();
        tenantConfigWrapper.eq(QwxTenantConfig::getTenantId, tenantId);
        remove(tenantConfigWrapper);
        
        // 重新创建默认配置
        for (QwxTenantConfig defaultConfig : defaultConfigs) {
            QwxTenantConfig newConfig = new QwxTenantConfig();
            newConfig.setTenantId(tenantId);
            newConfig.setConfigKey(defaultConfig.getConfigKey());
            newConfig.setConfigValue(defaultConfig.getConfigValue());
            newConfig.setConfigType(defaultConfig.getConfigType());
            newConfig.setIsDefault("N");
            newConfig.setConfigName(defaultConfig.getConfigName());
            newConfig.setConfigDesc(defaultConfig.getConfigDesc());
            newConfig.setOptions(defaultConfig.getOptions());
            newConfig.setOrderNum(defaultConfig.getOrderNum());
            newConfig.setStatus(defaultConfig.getStatus());
            save(newConfig);
        }
        
        return true;
    }
} 