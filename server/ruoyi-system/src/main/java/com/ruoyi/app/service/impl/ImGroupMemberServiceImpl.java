package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImGroup;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.mapper.ImGroupMapper;
import com.ruoyi.app.mapper.ImGroupMemberMapper;
import com.ruoyi.app.mapper.QwxUserTenantMapper;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.MemberPermissionsResponseVo;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import com.ruoyi.app.service.ImGroupMemberService;
import com.ruoyi.app.service.ImGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组成员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Service
@Slf4j
public class ImGroupMemberServiceImpl extends ServiceImpl<ImGroupMemberMapper, ImGroupMember> implements ImGroupMemberService {

    @Resource
    private QwxUserTenantMapper qwxUserTenantMapper;
    @Resource
    private ImGroupMapper imGroupMapper;

    @Override
    public List<ImGroupMember> selectMemberList(Long groupId, String keyword, String role, String muteStatus) {
        LambdaQueryWrapper<ImGroupMember> queryWrapper = new LambdaQueryWrapper<>();

        // 群组ID条件
        if (groupId != null) {
            queryWrapper.eq(ImGroupMember::getGroupId, groupId);
        }

        // 关键词搜索（昵称）
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(ImGroupMember::getNickname, keyword);
        }

        // 角色筛选
        if (StringUtils.hasText(role)) {
            queryWrapper.eq(ImGroupMember::getRole, role);
        }

        // 禁言状态筛选
        if (StringUtils.hasText(muteStatus)) {
            Date now = new Date();
            if ("1".equals(muteStatus)) { // 被禁言
                queryWrapper.and(wrapper -> wrapper.isNotNull(ImGroupMember::getMuteEndTime)
                        .gt(ImGroupMember::getMuteEndTime, now));
            } else if ("0".equals(muteStatus)) { // 未被禁言
                queryWrapper.and(wrapper -> wrapper.isNull(ImGroupMember::getMuteEndTime)
                        .or()
                        .le(ImGroupMember::getMuteEndTime, now));
            }
        }

        // 只查询正常状态的成员
        queryWrapper.eq(ImGroupMember::getStatus, "0");
        queryWrapper.orderByDesc(ImGroupMember::getJoinTime);

        return this.list(queryWrapper);
    }

    @Override
    public List<QwxUserTenantDetailVo> selectMemberListWithUserDetails(Long groupId, String keyword, String role) {
        return this.baseMapper.selectMemberListWithUserDetails(groupId, keyword, role);
    }

    @Override
    public ImGroupMember getMemberInfo(Long groupId, Long userId) {
        LambdaQueryWrapper<ImGroupMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImGroupMember::getGroupId, groupId)
                .eq(ImGroupMember::getUserId, userId)
                .eq(ImGroupMember::getStatus, "0");
        return this.getOne(queryWrapper);
    }

    @Override
    public int addMember(AddMemberRequestDto addMemberRequest) {
        try {
            // 1. 检查用户是否已经是群成员
            LambdaQueryWrapper<ImGroupMember> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(ImGroupMember::getGroupId, addMemberRequest.getGroupId())
                    .eq(ImGroupMember::getUserId, addMemberRequest.getUserId());
            
            ImGroupMember existingMember = this.getOne(checkWrapper);
            
            if (existingMember != null) {
                // 如果已存在记录，检查状态
                if ("0".equals(existingMember.getStatus())) {
                    // 用户已经是正常群成员，避免重复添加
                    log.warn("用户{}已经是群组{}的成员", addMemberRequest.getUserId(), addMemberRequest.getGroupId());
                    return 0;
                } else {
                    // 如果状态不是正常，重新激活成员身份
                    LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(ImGroupMember::getGroupId, addMemberRequest.getGroupId())
                            .eq(ImGroupMember::getUserId, addMemberRequest.getUserId())
                            .set(ImGroupMember::getStatus, "0")
                            .set(ImGroupMember::getJoinTime, new Date())
                            .set(ImGroupMember::getUpdateTime, new Date());
                    
                    if (this.update(updateWrapper)) {
                        // 2. 更新群组成员数量
                        imGroupMapper.updateGroupMemberCount(addMemberRequest.getGroupId());
                        log.info("重新激活用户{}在群组{}的成员身份", addMemberRequest.getUserId(), addMemberRequest.getGroupId());
                        return 1;
                    }
                    return 0;
                }
            }

            // 获取用户详情
            QwxUserTenantDetailVo tenantUserDetail = qwxUserTenantMapper.getTenantUserDetail(
                    addMemberRequest.getTenantId(), addMemberRequest.getUserId());

            if (tenantUserDetail == null) {
                log.error("获取用户详情失败，userId: {}, tenantId: {}", 
                        addMemberRequest.getUserId(), addMemberRequest.getTenantId());
                return 0;
            }

            // 创建新成员记录
            ImGroupMember member = new ImGroupMember();
            member.setGroupId(addMemberRequest.getGroupId());
            member.setTenantId(addMemberRequest.getTenantId());
            member.setUserId(addMemberRequest.getUserId());
            member.setInviteUserId(addMemberRequest.getInviteUserId());
            member.setJoinSource(addMemberRequest.getJoinSource());
            member.setNickname(tenantUserDetail.getNickName());
            member.setRole("0"); // 默认普通成员
            member.setJoinTime(new Date());
            member.setStatus("0"); // 正常状态
            member.setCreateTime(new Date());
            member.setUpdateTime(new Date());

            boolean saved = this.save(member);
            if (saved) {
                // 2. 更新群组成员数量
                imGroupMapper.updateGroupMemberCount(addMemberRequest.getGroupId());
                log.info("成功添加用户{}到群组{}", addMemberRequest.getUserId(), addMemberRequest.getGroupId());
                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("添加群成员失败, userId: {}, groupId: {}", 
                    addMemberRequest.getUserId(), addMemberRequest.getGroupId(), e);
            return 0;
        }
    }

    @Override
    public int batchAddMembers(BatchAddMembersRequestDto batchAddRequest) {
        try {
            int successCount = 0;
            ImGroup groupDetail = imGroupMapper.selectById(batchAddRequest.getGroupId());
            if (groupDetail == null) {
                log.error("群组不存在, groupId: {}", batchAddRequest.getGroupId());
                return 0;
            }

            for (Long userId : batchAddRequest.getUserIds()) {
                AddMemberRequestDto addRequest = new AddMemberRequestDto();
                addRequest.setGroupId(batchAddRequest.getGroupId());
                addRequest.setTenantId(groupDetail.getTenantId());
                addRequest.setUserId(userId);
                addRequest.setInviteUserId(batchAddRequest.getInviteUserId());
                addRequest.setJoinSource(batchAddRequest.getJoinSource());

                if (addMember(addRequest) > 0) {
                    successCount++;
                }
            }
            
            log.info("批量添加群成员完成, groupId: {}, 成功数量: {}", 
                    batchAddRequest.getGroupId(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量添加群成员失败, groupId: {}", batchAddRequest.getGroupId(), e);
            return 0;
        }
    }

    @Override
    public int removeMember(Long groupId, Long userId, Long operatorId, String leaveReason) {
        try {
            // 检查成员是否存在且状态正常
            LambdaQueryWrapper<ImGroupMember> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(ImGroupMember::getGroupId, groupId)
                    .eq(ImGroupMember::getUserId, userId)
                    .eq(ImGroupMember::getStatus, "0");
            
            ImGroupMember existingMember = this.getOne(checkWrapper);
            if (existingMember == null) {
                log.warn("要移除的成员不存在或已被移除, userId: {}, groupId: {}", userId, groupId);
                return 0;
            }

            LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ImGroupMember::getGroupId, groupId)
                    .eq(ImGroupMember::getUserId, userId)
                    .eq(ImGroupMember::getStatus, "0")
                    .set(ImGroupMember::getStatus, "1")
                    .set(ImGroupMember::getLeaveTime, new Date())
                    .set(ImGroupMember::getUpdateTime, new Date());

            if (StringUtils.hasText(leaveReason)) {
                updateWrapper.set(ImGroupMember::getLeaveReason, leaveReason);
            }

            boolean updated = this.update(updateWrapper);
            if (updated) {
                // 1. 更新群组成员数量（减少）
                imGroupMapper.updateGroupMemberCount(groupId);
                log.info("成功移除用户{}从群组{}", userId, groupId);
                return 1;
            }
            return 0;
        } catch (Exception e) {
            log.error("移除群成员失败, userId: {}, groupId: {}", userId, groupId, e);
            return 0;
        }
    }

    @Override
    public int batchRemoveMembers(BatchRemoveMembersRequestDto batchRemoveRequest) {
        try {
            int successCount = 0;
            for (Long userId : batchRemoveRequest.getUserIds()) {
                if (removeMember(batchRemoveRequest.getGroupId(), userId,
                        batchRemoveRequest.getOperatorId(), batchRemoveRequest.getLeaveReason()) > 0) {
                    successCount++;
                }
            }
            
            log.info("批量移除群成员完成, groupId: {}, 成功数量: {}", 
                    batchRemoveRequest.getGroupId(), successCount);
            return successCount;
        } catch (Exception e) {
            log.error("批量移除群成员失败, groupId: {}", batchRemoveRequest.getGroupId(), e);
            return 0;
        }
    }

    @Override
    public int muteMember(MuteMemberRequestDto muteRequest) {
        Date muteEndTime = muteRequest.getMuteEndTime();

        // 如果没有指定结束时间但有时长，则计算结束时间
        if (muteEndTime == null && muteRequest.getMuteDuration() != null) {
            muteEndTime = new Date(System.currentTimeMillis() + muteRequest.getMuteDuration() * 60 * 1000L);
        }

        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, muteRequest.getGroupId())
                .eq(ImGroupMember::getUserId, muteRequest.getUserId())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getMuteEndTime, muteEndTime)
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    public int unmuteMember(UnmuteMemberRequestDto unmuteRequest) {
        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, unmuteRequest.getGroupId())
                .eq(ImGroupMember::getUserId, unmuteRequest.getUserId())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getMuteEndTime, null)
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    public int batchMuteMembers(BatchMuteMembersRequestDto batchMuteRequest) {
        Date muteEndTime = batchMuteRequest.getMuteEndTime();

        // 如果没有指定结束时间但有时长，则计算结束时间
        if (muteEndTime == null && batchMuteRequest.getMuteDuration() != null) {
            muteEndTime = new Date(System.currentTimeMillis() + batchMuteRequest.getMuteDuration() * 60 * 1000L);
        }

        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, batchMuteRequest.getGroupId())
                .in(ImGroupMember::getUserId, batchMuteRequest.getUserIds())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getMuteEndTime, muteEndTime)
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? batchMuteRequest.getUserIds()
                .size() : 0;
    }

    @Override
    public int setMemberAsAdmin(SetMemberAdminRequestDto setAdminRequest) {
        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, setAdminRequest.getGroupId())
                .eq(ImGroupMember::getUserId, setAdminRequest.getUserId())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getRole, "1") // 设置为管理员
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    public int unsetMemberAsAdmin(SetMemberAdminRequestDto unsetAdminRequest) {
        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, unsetAdminRequest.getGroupId())
                .eq(ImGroupMember::getUserId, unsetAdminRequest.getUserId())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getRole, "0") // 设置为普通成员
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    public int updateMemberNickname(UpdateMemberNicknameRequestDto nicknameRequest) {
        LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImGroupMember::getGroupId, nicknameRequest.getGroupId())
                .eq(ImGroupMember::getUserId, nicknameRequest.getUserId())
                .eq(ImGroupMember::getStatus, "0")
                .set(ImGroupMember::getNickname, nicknameRequest.getNewNickname())
                .set(ImGroupMember::getUpdateTime, new Date());

        return this.update(updateWrapper) ? 1 : 0;
    }

    @Override
    public MemberPermissionsResponseVo getMemberPermissions(Long groupId, Long userId) {
        ImGroupMember member = getMemberInfo(groupId, userId);
        if (member == null) {
            return null;
        }

        MemberPermissionsResponseVo vo = new MemberPermissionsResponseVo();
        BeanUtils.copyProperties(member, vo);

        // 判断是否被禁言
        Date now = new Date();
        boolean isMuted = member.getMuteEndTime() != null && member.getMuteEndTime()
                .after(now);
        vo.setIsMuted(isMuted);

        return vo;
    }

    @Override
    public int batchUpdatePermissions(BatchUpdatePermissionsRequestDto batchPermissionRequest) {
        int successCount = 0;
        for (BatchUpdatePermissionsRequestDto.UserPermissionConfig config : batchPermissionRequest.getUserPermissions()) {
            LambdaUpdateWrapper<ImGroupMember> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ImGroupMember::getGroupId, batchPermissionRequest.getGroupId())
                    .eq(ImGroupMember::getUserId, config.getUserId())
                    .eq(ImGroupMember::getStatus, "0")
                    .set(ImGroupMember::getRole, config.getRole())
                    .set(ImGroupMember::getUpdateTime, new Date());

            if (this.update(updateWrapper)) {
                successCount++;
            }
        }
        return successCount;
    }

    @Override
    public List<ImGroupMember> getMemberHistory(Long groupId, Long userId) {
        LambdaQueryWrapper<ImGroupMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImGroupMember::getGroupId, groupId)
                .eq(ImGroupMember::getUserId, userId)
                .orderByDesc(ImGroupMember::getCreateTime);

        return this.list(queryWrapper);
    }

}
