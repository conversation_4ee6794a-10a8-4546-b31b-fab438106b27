package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 添加成员请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "添加成员请求参数", description = "用于拉人入群的请求参数")
public class AddMemberRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "2", notes = "要添加的用户ID")
    private Long userId;

    /**
     * 邀请人ID
     */
    @ApiModelProperty(value = "邀请人ID", required = true, example = "1", notes = "邀请人的用户ID")
    private Long inviteUserId;

    /**
     * 加入来源
     */
    @ApiModelProperty(value = "加入来源", example = "1", notes = "加入来源（0直接加入 1邀请加入 2扫码加入 3审批加入）")
    private String joinSource;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "1", notes = "租户ID")
    private Long tenantId;

} 