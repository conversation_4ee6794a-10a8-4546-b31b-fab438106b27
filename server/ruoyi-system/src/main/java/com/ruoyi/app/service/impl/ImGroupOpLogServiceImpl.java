package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImGroupOpLog;
import com.ruoyi.app.mapper.ImGroupOpLogMapper;
import com.ruoyi.app.service.ImGroupOpLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组操作日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Service
public class ImGroupOpLogServiceImpl extends ServiceImpl<ImGroupOpLogMapper, ImGroupOpLog> implements ImGroupOpLogService {

    @Resource
    private ImGroupOpLogMapper imGroupOpLogMapper;

    @Override
    public boolean logGroupOperation(Long tenantId, Long groupId, Long operatorId, 
                                     Long targetUserId, String operationType, String operationDesc,
                                     String beforeData, String afterData, 
                                     String clientIp, String userAgent) {
        try {
            ImGroupOpLog opLog = new ImGroupOpLog();
            opLog.setTenantId(tenantId);
            opLog.setGroupId(groupId);
            opLog.setOperatorId(operatorId);
            opLog.setTargetUserId(targetUserId);
            opLog.setOperationType(operationType);
            opLog.setOperationDesc(operationDesc);
            opLog.setBeforeData(beforeData);
            opLog.setAfterData(afterData);
            opLog.setClientIp(clientIp);
            opLog.setUserAgent(userAgent);
            opLog.setCreateTime(new Date());
            opLog.setStatus("0"); // 成功
            
            return save(opLog);
        } catch (Exception e) {
            // 记录失败日志
            ImGroupOpLog opLog = new ImGroupOpLog();
            opLog.setTenantId(tenantId);
            opLog.setGroupId(groupId);
            opLog.setOperatorId(operatorId);
            opLog.setTargetUserId(targetUserId);
            opLog.setOperationType(operationType);
            opLog.setOperationDesc(operationDesc);
            opLog.setBeforeData(beforeData);
            opLog.setAfterData(afterData);
            opLog.setClientIp(clientIp);
            opLog.setUserAgent(userAgent);
            opLog.setCreateTime(new Date());
            opLog.setStatus("1"); // 失败
            opLog.setErrorMsg(e.getMessage());
            
            save(opLog);
            return false;
        }
    }

    @Override
    public List<ImGroupOpLog> getLogsByGroupId(Long tenantId, Long groupId, Date startTime, Date endTime) {
        return imGroupOpLogMapper.selectByGroupId(tenantId, groupId, startTime, endTime);
    }

    @Override
    public List<ImGroupOpLog> getLogsByOperatorId(Long tenantId, Long operatorId, Date startTime, Date endTime) {
        return imGroupOpLogMapper.selectByOperatorId(tenantId, operatorId, startTime, endTime);
    }
} 