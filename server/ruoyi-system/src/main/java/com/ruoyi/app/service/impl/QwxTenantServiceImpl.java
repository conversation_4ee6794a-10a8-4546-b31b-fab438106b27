package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.QwxTenant;
import com.ruoyi.app.mapper.QwxTenantMapper;
import com.ruoyi.app.service.QwxTenantService;
import com.ruoyi.common.annotation.DataScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 租户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Service
public class QwxTenantServiceImpl extends ServiceImpl<QwxTenantMapper, QwxTenant> implements QwxTenantService {
    @Resource
    private QwxTenantMapper qwxTenantMapper;
    /**
     * 查询租户列表
     *
     * @param qwxTenant 租户
     * @return 租户
     */
    @Override
    @DataScope(deptAlias = "t")
    public List<QwxTenant> selectQwxTenantList(QwxTenant qwxTenant)
    {
        return qwxTenantMapper.selectQwxTenantList(qwxTenant);
    }

}
