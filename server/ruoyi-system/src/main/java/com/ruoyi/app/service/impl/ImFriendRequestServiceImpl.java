package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImFriendRequest;
import com.ruoyi.app.mapper.ImFriendRequestMapper;
import com.ruoyi.app.service.ImFriendRequestService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 好友申请记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Service
public class ImFriendRequestServiceImpl extends ServiceImpl<ImFriendRequestMapper, ImFriendRequest> implements ImFriendRequestService {

    @Resource
    private ImFriendRequestMapper imFriendRequestMapper;

    @Override
    public List<ImFriendRequest> getByReceiverUserId(Long tenantId, Long receiverUserId, String status) {
        return imFriendRequestMapper.selectByReceiverUserId(tenantId, receiverUserId, status);
    }

    @Override
    public ImFriendRequest getBySenderAndReceiver(Long tenantId, Long senderUserId, Long receiverUserId) {
        return imFriendRequestMapper.selectBySenderAndReceiver(tenantId, senderUserId, receiverUserId);
    }

    @Override
    public boolean createFriendRequest(ImFriendRequest friendRequest) {
        friendRequest.setCreateTime(new Date());
        friendRequest.setUpdateTime(new Date());
        return save(friendRequest);
    }

    @Override
    public boolean updateRequestStatus(Long id, String status) {
        ImFriendRequest friendRequest = new ImFriendRequest();
        friendRequest.setId(id);
        friendRequest.setStatus(status);
        friendRequest.setResponseTime(new Date());
        friendRequest.setUpdateTime(new Date());
        return updateById(friendRequest);
    }
}
