package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 设置成员管理员请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "设置成员管理员请求参数", description = "用于设置或取消成员管理员身份的请求参数")
public class SetMemberAdminRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "2", notes = "要设置管理员的用户ID")
    private Long userId;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1", notes = "执行操作的用户ID")
    private Long operatorId;
} 