package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.QwxTenantInviteCode;
import com.ruoyi.app.mapper.QwxTenantInviteCodeMapper;
import com.ruoyi.app.service.QwxTenantInviteCodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 租户邀请码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Service
public class QwxTenantInviteCodeServiceImpl extends ServiceImpl<QwxTenantInviteCodeMapper, QwxTenantInviteCode> implements QwxTenantInviteCodeService {
    @Resource
    private QwxTenantInviteCodeMapper qwxTenantInviteCodeMapper;
    /**
     * 查询租户邀请码列表
     *
     * @param qwxTenantInviteCode 租户邀请码
     * @return 租户邀请码
     */
    @Override
    public List<QwxTenantInviteCode> selectQwxTenantInviteCodeList(QwxTenantInviteCode qwxTenantInviteCode)
    {
        return qwxTenantInviteCodeMapper.selectQwxTenantInviteCodeList(qwxTenantInviteCode);
    }
}
