package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 配置开关请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "配置开关请求参数", description = "用于启用/禁用配置的请求参数")
public class ConfigSwitchRequestDto {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    @ApiModelProperty(value = "配置ID", required = true, example = "1", notes = "要切换状态的配置ID")
    private Long configId;

    /**
     * 开关状态
     */
    @NotNull(message = "开关状态不能为空")
    @ApiModelProperty(value = "开关状态", required = true, example = "0", notes = "状态（0正常 1停用）", allowableValues = "0,1")
    private String status;
} 