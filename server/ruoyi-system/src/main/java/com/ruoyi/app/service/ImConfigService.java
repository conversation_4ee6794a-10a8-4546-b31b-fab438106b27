package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImConfig;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.ConfigResponseVo;
import com.ruoyi.app.model.vo.NeteaseConnectionTestResponseVo;
import com.ruoyi.app.model.vo.SystemConfigResponseVo;

import java.util.List;

/**
 * <p>
 * IM配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImConfigService extends IService<ImConfig> {

    /**
     * 根据租户ID和配置键获取配置值
     * 
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(Long tenantId, String configKey);

    /**
     * 根据租户ID和配置键获取配置值，带默认值
     * 
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(Long tenantId, String configKey, String defaultValue);

    /**
     * 根据租户ID获取所有配置
     * 
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<ImConfig> getConfigsByTenantId(Long tenantId);

    /**
     * 获取系统配置
     * 
     * @return 系统配置列表
     */
    List<ImConfig> getSystemConfigs();

    /**
     * 根据配置类型查询配置
     * 
     * @param tenantId 租户ID
     * @param configType 配置类型
     * @return 配置列表
     */
    List<ImConfig> getConfigsByType(Long tenantId, String configType);

    /**
     * 更新配置值
     * 
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @param configValue 配置值
     * @return 是否更新成功
     */
    boolean updateConfigValue(Long tenantId, String configKey, String configValue);

    /**
     * 批量更新配置
     * 
     * @param tenantId 租户ID
     * @param configs 配置列表
     * @return 是否更新成功
     */
    boolean batchUpdateConfigs(Long tenantId, List<ImConfig> configs);

    /**
     * 初始化租户配置（从系统配置复制）
     * 
     * @param tenantId 租户ID
     * @return 是否初始化成功
     */
    boolean initTenantConfigs(Long tenantId);

    /**
     * 重置租户配置为系统默认配置
     * 
     * @param tenantId 租户ID
     * @return 是否重置成功
     */
    boolean resetTenantConfigs(Long tenantId);

    /**
     * 查询配置列表
     * 
     * @param queryRequest 查询请求参数
     * @return 配置列表
     */
    List<ImConfig> selectConfigList(ConfigQueryRequestDto queryRequest);

    /**
     * 根据配置键获取配置（脱敏）
     * 
     * @param configKey 配置键
     * @return 配置响应VO
     */
    ConfigResponseVo getConfigByKey(String configKey);

    /**
     * 获取网易云信AppKey配置
     * 
     * @return AppKey配置值
     */
    String getNeteaseAppKey();

    /**
     * 获取网易云信AppSecret配置（脱敏）
     * 
     * @return 脱敏后的AppSecret配置值
     */
    String getNeteaseAppSecretMasked();

    /**
     * 新增配置
     * 
     * @param request 新增配置请求
     * @return 操作结果
     */
    int insertConfig(CreateConfigRequestDto request);

    /**
     * 更新配置
     * 
     * @param request 更新配置请求
     * @return 操作结果
     */
    int updateConfig(UpdateConfigRequestDto request);

    /**
     * 更新网易云信AppKey
     * 
     * @param request 更新AppKey请求
     * @return 操作结果
     */
    int updateNeteaseAppKey(UpdateNeteaseAppKeyRequestDto request);

    /**
     * 更新网易云信AppSecret
     * 
     * @param request 更新AppSecret请求
     * @return 操作结果
     */
    int updateNeteaseAppSecret(UpdateNeteaseAppSecretRequestDto request);

    /**
     * 删除配置
     * 
     * @param configIds 配置ID数组
     * @return 操作结果
     */
    int deleteConfigByIds(Long[] configIds);

    /**
     * 配置开关切换
     * 
     * @param request 开关请求
     * @return 操作结果
     */
    int switchConfig(ConfigSwitchRequestDto request);

    /**
     * 测试网易云信连接
     * 
     * @return 测试结果
     */
    NeteaseConnectionTestResponseVo testNeteaseConnection();

    /**
     * 刷新配置缓存
     */
    void refreshCache();

    /**
     * 获取系统配置信息
     * 
     * @return 系统配置响应VO
     */
    SystemConfigResponseVo getSystemConfig();

    /**
     * 导入配置文件
     * 
     * @param request 导入请求
     * @return 导入结果
     */
    String importConfig(ConfigImportRequestDto request);
} 