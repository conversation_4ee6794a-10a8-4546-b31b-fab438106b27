package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImCloudRequest;
import com.ruoyi.app.mapper.ImCloudRequestMapper;
import com.ruoyi.app.service.ImCloudRequestService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 云信接口幂等记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Service
public class ImCloudRequestServiceImpl extends ServiceImpl<ImCloudRequestMapper, ImCloudRequest> implements ImCloudRequestService {

    @Resource
    private ImCloudRequestMapper imCloudRequestMapper;

    @Override
    public Long startRequest(Long tenantId, String requestUuid, String apiName, 
                             String requestUrl, String requestMethod, 
                             String requestHeaders, String requestBody) {
        ImCloudRequest request = new ImCloudRequest();
        request.setTenantId(tenantId);
        request.setRequestUuid(requestUuid);
        request.setApiName(apiName);
        request.setRequestUrl(requestUrl);
        request.setRequestMethod(requestMethod);
        request.setRequestHeaders(requestHeaders);
        request.setRequestBody(requestBody);
        request.setRetryCount(0);
        request.setStatus("0"); // 进行中
        request.setCreateTime(new Date());
        
        save(request);
        return request.getRequestId();
    }

    @Override
    public boolean finishRequest(String requestUuid, Integer responseCode, String responseBody, 
                                 Integer costTime, String status, String errorMsg) {
        LambdaQueryWrapper<ImCloudRequest> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImCloudRequest::getRequestUuid, requestUuid);
        
        ImCloudRequest request = getOne(queryWrapper);
        if (request != null) {
            request.setResponseCode(responseCode);
            request.setResponseBody(responseBody);
            request.setCostTime(costTime);
            request.setStatus(status);
            request.setErrorMsg(errorMsg);
            request.setFinishTime(new Date());
            
            return updateById(request);
        }
        return false;
    }

    @Override
    public ImCloudRequest getByRequestUuid(String requestUuid) {
        return imCloudRequestMapper.selectByRequestUuid(requestUuid);
    }

    @Override
    public boolean isRequestExists(String requestUuid) {
        return getByRequestUuid(requestUuid) != null;
    }

    @Override
    public List<ImCloudRequest> getRequestsByTenantAndApi(Long tenantId, String apiName, 
                                                          Date startTime, Date endTime) {
        return imCloudRequestMapper.selectByTenantIdAndApi(tenantId, apiName, startTime, endTime);
    }

    @Override
    public List<ImCloudRequest> getFailedRequests(Long tenantId, Integer maxRetryCount) {
        return imCloudRequestMapper.selectFailedRequests(tenantId, maxRetryCount);
    }

    @Override
    public boolean retryRequest(String requestUuid) {
        LambdaQueryWrapper<ImCloudRequest> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImCloudRequest::getRequestUuid, requestUuid);
        
        ImCloudRequest request = getOne(queryWrapper);
        if (request != null) {
            request.setRetryCount(request.getRetryCount() + 1);
            request.setStatus("0"); // 重新设置为进行中
            request.setErrorMsg("");
            
            return updateById(request);
        }
        return false;
    }

    @Override
    public int cleanExpiredRecords(Date expireTime) {
        return imCloudRequestMapper.deleteExpiredRecords(expireTime);
    }

    @Override
    public String generateRequestUuid(Long tenantId, String apiName, String requestParams) {
        try {
            // 使用租户ID + 接口名称 + 请求参数的MD5作为UUID
            String source = tenantId + "_" + apiName + "_" + (StringUtils.isNotEmpty(requestParams) ? requestParams : "");
            
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(source.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (Exception e) {
            // 如果生成失败，使用UUID
            return UUID.randomUUID().toString().replace("-", "");
        }
    }
} 