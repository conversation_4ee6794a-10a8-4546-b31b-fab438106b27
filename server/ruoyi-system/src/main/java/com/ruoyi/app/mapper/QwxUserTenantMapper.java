package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.vo.AppUserTenantIMVo;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户租户关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface QwxUserTenantMapper extends BaseMapper<QwxUserTenant> {
    
    /**
     * 获取用户的所有租户及云信账号信息
     *
     * @param globalUserId 全局用户ID
     * @return 租户和云信账号信息列表
     */
    List<AppUserTenantIMVo.TenantIMInfo> selectUserTenantAndIMInfoList(@Param("globalUserId") String globalUserId);

    /**
     * 获取用户的指定租户及云信账号信息
     *
     * @param globalUserId 全局用户ID
     * @param tenantId 租户ID
     * @return 租户和云信账号信息
     */
    AppUserTenantIMVo.TenantIMInfo selectSingleUserTenantAndIMInfo(@Param("globalUserId") String globalUserId, @Param("tenantId") String tenantId);

    /**
     * 查询租户下用户详细信息（包含联系方式）
     * 通过SQL关联查询qwx_user_tenant和qwx_global_user表
     *
     * @param tenantId 租户ID
     * @param status 用户状态
     * @return 用户详细信息列表
     */
    List<QwxUserTenantDetailVo> selectTenantUserDetailList(@Param("tenantId") Long tenantId, @Param("status") String status);

    /**
     * 查询租户下用户详细信息（支持查询条件）
     * 通过SQL关联查询qwx_user_tenant和qwx_global_user表，支持分页查询
     *
     * @param qwxUserTenant 查询条件
     * @return 用户详细信息列表
     */
    List<QwxUserTenantDetailVo> selectTenantUserDetailListWithConditions(@Param("qwxUserTenant") QwxUserTenant qwxUserTenant);

    /**
     * 获取租户指定userId的用户详细信息
     *
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return 用户详细信息
     */
    QwxUserTenantDetailVo getTenantUserDetail(@Param("tenantId") Long tenantId, @Param("userId") Long userId);
}
