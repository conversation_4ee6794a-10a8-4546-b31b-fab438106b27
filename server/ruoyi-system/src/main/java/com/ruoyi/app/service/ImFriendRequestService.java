package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImFriendRequest;

import java.util.List;

/**
 * <p>
 * 好友申请记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
public interface ImFriendRequestService extends IService<ImFriendRequest> {

    /**
     * 根据接收者用户ID查询好友申请列表
     * 
     * @param tenantId 租户ID
     * @param receiverUserId 接收者用户ID
     * @param status 申请状态（可选）
     * @return 好友申请列表
     */
    List<ImFriendRequest> getByReceiverUserId(Long tenantId, Long receiverUserId, String status);

    /**
     * 根据发送者和接收者查询好友申请记录
     * 
     * @param tenantId 租户ID
     * @param senderUserId 发送者用户ID
     * @param receiverUserId 接收者用户ID
     * @return 好友申请记录
     */
    ImFriendRequest getBySenderAndReceiver(Long tenantId, Long senderUserId, Long receiverUserId);

    /**
     * 创建好友申请记录
     * 
     * @param friendRequest 好友申请记录
     * @return 是否创建成功
     */
    boolean createFriendRequest(ImFriendRequest friendRequest);

    /**
     * 更新好友申请状态
     * 
     * @param id 申请记录ID
     * @param status 新状态
     * @return 是否更新成功
     */
    boolean updateRequestStatus(Long id, String status);
}
