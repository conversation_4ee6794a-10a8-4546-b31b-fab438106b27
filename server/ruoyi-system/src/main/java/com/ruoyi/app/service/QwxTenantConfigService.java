package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.QwxTenantConfig;

import java.util.List;

/**
 * 租户配置Service接口
 * 
 * <AUTHOR>
 */
public interface QwxTenantConfigService extends IService<QwxTenantConfig> {
    
    /**
     * 查询租户配置列表
     *
     * @param tenantConfig 租户配置
     * @return 租户配置集合
     */
    List<QwxTenantConfig> selectTenantConfigList(QwxTenantConfig tenantConfig);
    
    /**
     * 根据租户ID和配置键获取配置值
     *
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(Long tenantId, String configKey);
    
    /**
     * 根据租户ID获取所有配置
     *
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<QwxTenantConfig> getConfigByTenantId(Long tenantId);
    
    /**
     * 新增租户配置
     *
     * @param tenantConfig 租户配置
     * @return 结果
     */
    boolean insertTenantConfig(QwxTenantConfig tenantConfig);
    
    /**
     * 修改租户配置
     *
     * @param tenantConfig 租户配置
     * @return 结果
     */
    boolean updateTenantConfig(QwxTenantConfig tenantConfig);
    
    /**
     * 批量更新租户配置
     *
     * @param tenantId 租户ID
     * @param configs 配置列表
     * @return 结果
     */
    boolean updateBatch(Long tenantId, List<QwxTenantConfig> configs);
    
    /**
     * 批量删除租户配置
     *
     * @param configIds 需要删除的租户配置ID
     * @return 结果
     */
    boolean deleteTenantConfigByIds(Long[] configIds);
    
    /**
     * 重置租户配置为默认值
     *
     * @param tenantId 租户ID
     * @return 结果
     */
    boolean resetToDefault(Long tenantId);
} 