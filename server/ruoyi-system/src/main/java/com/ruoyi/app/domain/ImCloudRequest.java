package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 云信接口幂等记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_cloud_request")
public class ImCloudRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求ID
     */
    @TableId(value = "request_id", type = IdType.AUTO)
    private Long requestId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 请求唯一标识
     */
    private String requestUuid;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 请求头（JSON格式）
     */
    private String requestHeaders;

    /**
     * 请求体
     */
    private String requestBody;

    /**
     * 响应状态码
     */
    private Integer responseCode;

    /**
     * 响应体
     */
    private String responseBody;

    /**
     * 耗时（毫秒）
     */
    private Integer costTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 状态（0进行中 1成功 2失败 3超时）
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 完成时间
     */
    private Date finishTime;
} 