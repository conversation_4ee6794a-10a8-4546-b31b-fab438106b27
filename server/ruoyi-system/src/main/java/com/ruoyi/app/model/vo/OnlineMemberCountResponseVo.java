package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 在线成员数量响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "在线成员数量响应", description = "群组在线成员数量统计响应")
public class OnlineMemberCountResponseVo {

    /**
     * 群组ID
     */
    @ApiModelProperty(value = "群组ID", example = "1", notes = "群组唯一标识")
    private Long groupId;

    /**
     * 在线人数
     */
    @ApiModelProperty(value = "在线人数", example = "25", notes = "当前群组在线成员数量")
    private Long onlineCount;

    /**
     * 总人数
     */
    @ApiModelProperty(value = "总人数", example = "50", notes = "群组总成员数量")
    private Long totalCount;
} 