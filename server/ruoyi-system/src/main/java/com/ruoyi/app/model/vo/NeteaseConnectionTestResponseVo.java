package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 网易云信连接测试响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "网易云信连接测试响应参数", description = "网易云信连接测试结果响应参数")
public class NeteaseConnectionTestResponseVo {

    /**
     * 测试结果
     */
    @ApiModelProperty(value = "测试结果", example = "true", notes = "连接测试是否成功")
    private Boolean success;

    /**
     * 测试消息
     */
    @ApiModelProperty(value = "测试消息", example = "连接成功", notes = "测试结果描述信息")
    private String message;

    /**
     * 响应时间（毫秒）
     */
    @ApiModelProperty(value = "响应时间", example = "200", notes = "连接响应时间（毫秒）")
    private Long responseTime;

    /**
     * 错误代码
     */
    @ApiModelProperty(value = "错误代码", example = "200", notes = "网易云信返回的错误代码")
    private String errorCode;

    /**
     * 错误详情
     */
    @ApiModelProperty(value = "错误详情", example = "", notes = "详细的错误信息")
    private String errorDetail;
} 