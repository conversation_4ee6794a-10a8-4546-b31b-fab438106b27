package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 群组成员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface ImGroupMemberMapper extends BaseMapper<ImGroupMember> {

    /**
     * 查询群成员列表（包含用户详细信息）
     *
     * @param groupId 群组ID
     * @param keyword 关键词（昵称、用户名、手机号、邮箱）
     * @param role 角色
     * @return 群成员详细信息列表
     */
    List<QwxUserTenantDetailVo> selectMemberListWithUserDetails(@Param("groupId") Long groupId,
                                                                @Param("keyword") String keyword,
                                                                @Param("role") String role);

}
