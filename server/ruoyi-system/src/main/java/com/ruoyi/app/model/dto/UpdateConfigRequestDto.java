package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 更新配置请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "更新配置请求参数", description = "用于更新IM配置的请求参数")
public class UpdateConfigRequestDto {

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    @ApiModelProperty(value = "配置ID", required = true, example = "1", notes = "要更新的配置ID")
    private Long configId;

    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值", example = "your_app_key_here", notes = "配置值")
    private String configValue;

    /**
     * 配置名称
     */
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    @ApiModelProperty(value = "配置名称", example = "网易云信AppKey", notes = "配置名称最大长度100字符")
    private String configName;

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    @ApiModelProperty(value = "配置描述", example = "网易云信应用的AppKey", notes = "配置描述最大长度500字符")
    private String configDesc;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型", example = "text", notes = "配置类型：text文本 number数字 boolean布尔 json对象")
    private String configType;

    /**
     * 是否加密
     */
    @ApiModelProperty(value = "是否加密", example = "0", notes = "是否加密（0否 1是）", allowableValues = "0,1")
    private String isEncrypted;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序", example = "1", notes = "显示顺序")
    private Integer orderNum;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "0", notes = "状态（0正常 1停用）", allowableValues = "0,1")
    private String status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注", example = "配置备注信息", notes = "备注最大长度500字符")
    private String remark;
} 