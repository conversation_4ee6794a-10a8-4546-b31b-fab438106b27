package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 系统配置响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "系统配置响应参数", description = "系统配置信息响应参数")
public class SystemConfigResponseVo {

    /**
     * 网易云信配置
     */
    @ApiModelProperty(value = "网易云信配置", notes = "网易云信相关配置信息")
    private Map<String, Object> neteaseConfig;

    /**
     * 系统配置
     */
    @ApiModelProperty(value = "系统配置", notes = "系统相关配置信息")
    private Map<String, Object> systemConfig;

    /**
     * 功能开关配置
     */
    @ApiModelProperty(value = "功能开关配置", notes = "功能开关相关配置")
    private Map<String, Object> featureConfig;

    /**
     * 安全配置
     */
    @ApiModelProperty(value = "安全配置", notes = "安全相关配置信息")
    private Map<String, Object> securityConfig;
} 