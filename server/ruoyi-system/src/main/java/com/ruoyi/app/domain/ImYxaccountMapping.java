package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 云信账号映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ImYxaccountMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 系统用户ID
     */
    private Long userId;

    /**
     * 全局用户ID
     */
    private String globalUserId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 云信账号ID
     */
    private String yxAccountId;

    /**
     * 云信Token
     */
    private String yxToken;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
