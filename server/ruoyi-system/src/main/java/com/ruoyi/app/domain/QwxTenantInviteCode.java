package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 租户邀请码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxTenantInviteCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 邀请码ID
     */
    @TableId(value = "code_id", type = IdType.AUTO)
    private Long codeId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请码名称
     */
    private String codeName;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 最大使用次数（0表示不限制）
     */
    private Integer maxUses;

    /**
     * 已使用次数
     */
    private Integer usedCount;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 状态（0有效 1已禁用 2已过期 3已用尽）
     */
    private String status;

    /**
     * 是否自动激活账号（0否 1是）
     */
    private String autoActivate;

    /**
     * 是否需要审核（0否 1是）
     */
    private String needApproval;

    /**
     * 默认角色
     */
    private String defaultRole;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


}
