package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 禁言成员请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "禁言成员请求参数", description = "用于设置群成员禁言的请求参数")
public class MuteMemberRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "2", notes = "要禁言的用户ID")
    private Long userId;

    /**
     * 禁言结束时间
     */
    @ApiModelProperty(value = "禁言结束时间", example = "2024-12-31 23:59:59", notes = "禁言结束时间，为空表示永久禁言")
    private Date muteEndTime;

    /**
     * 禁言时长（分钟）
     */
    @ApiModelProperty(value = "禁言时长", example = "60", notes = "禁言时长（分钟），与禁言结束时间二选一")
    private Integer muteDuration;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1", notes = "执行禁言操作的用户ID")
    private Long operatorId;
} 