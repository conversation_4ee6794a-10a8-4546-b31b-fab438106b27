package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建群组请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "创建群组请求参数", description = "用于创建新群组的请求参数")
public class CreateGroupRequestDto {

    /**
     * 租户ID
     */
    @NotNull(message = "租户ID不能为空")
    @ApiModelProperty(value = "租户ID", required = true, example = "1", notes = "指定群组所属的租户ID")
    private Long tenantId;

    /**
     * 群主用户ID
     */
    @NotNull(message = "群主用户ID不能为空")
    @ApiModelProperty(value = "群主用户ID", required = true, example = "1", notes = "指定群组的群主用户ID")
    private Long ownerId;

    /**
     * 群组名称
     */
    @NotBlank(message = "群组名称不能为空")
    @Size(max = 50, message = "群组名称长度不能超过50个字符")
    @ApiModelProperty(value = "群组名称", required = true, example = "技术交流群", notes = "群组名称不能为空，最大长度50字符")
    private String groupName;

    /**
     * 群类型（1高级群 2超大群）
     */
    @NotBlank(message = "群类型不能为空")
    @ApiModelProperty(value = "群类型", required = true, example = "1", notes = "1高级群 2超大群", allowableValues = "1,2")
    private String groupType;

    /**
     * 群组头像
     */
    @ApiModelProperty(value = "群组头像", example = "https://example.com/avatar.jpg", notes = "群组头像URL")
    private String groupAvatar;

    /**
     * 群介绍
     */
    @Size(max = 200, message = "群介绍长度不能超过200个字符")
    @ApiModelProperty(value = "群介绍", example = "这是一个技术交流群", notes = "群介绍最大长度200字符")
    private String introduction;

    /**
     * 群公告
     */
    @Size(max = 500, message = "群公告长度不能超过500个字符")
    @ApiModelProperty(value = "群公告", example = "欢迎大家加入技术交流群，请文明讨论", notes = "群公告最大长度500字符")
    private String announcement;

    /**
     * 最大成员数
     */
    @ApiModelProperty(value = "最大成员数", example = "200", notes = "普通群默认200人，高级群默认2000人")
    private Integer maxMemberCount;

    /**
     * 加入方式（0自由加入 1需审批 2禁止加入）
     */
    @ApiModelProperty(value = "加入方式", example = "1", notes = "0自由加入 1需审批 2禁止加入", allowableValues = "0,1,2")
    private String joinMode;

    /**
     * 邀请权限（0所有人 1仅群主和管理员）
     */
    @ApiModelProperty(value = "邀请权限", example = "1", notes = "0所有人 1仅群主和管理员", allowableValues = "0,1")
    private String inviteMode;

    /**
     * 修改群信息权限（0群主和管理员 1所有人）
     */
    @ApiModelProperty(value = "修改群信息权限", example = "0", notes = "0群主和管理员 1所有人", allowableValues = "0,1")
    private String updateInfoMode;

    /**
     * 被邀请人同意方式（0需要同意 1不需要同意）
     */
    @ApiModelProperty(value = "被邀请人同意方式", example = "0", notes = "0需要同意 1不需要同意", allowableValues = "0,1")
    private String beInviteMode;

    /**
     * 初始成员ID列表
     */
    @ApiModelProperty(value = "初始成员ID列表", example = "[1,2,3]", notes = "要添加到群组的初始成员用户ID列表")
    private List<Long> memberIds;

    /**
     * 扩展字段（JSON格式）
     */
    @ApiModelProperty(value = "扩展字段", example = "{\"key\":\"value\"}", notes = "JSON格式的扩展字段")
    private String extension;

    /**
     * 自定义字段（JSON格式）
     */
    @ApiModelProperty(value = "自定义字段", example = "{\"customKey\":\"customValue\"}", notes = "JSON格式的自定义字段")
    private String customField;

    /**
     * 网易云信群组ID
     */
    @ApiModelProperty(value = "网易云信群组ID", example = "123456789", notes = "创建群组成功后网易云信返回的群组ID")
    private String yxGroupId;
} 