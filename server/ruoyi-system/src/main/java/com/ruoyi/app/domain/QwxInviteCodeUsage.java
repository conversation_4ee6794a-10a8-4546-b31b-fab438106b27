package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 邀请码使用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxInviteCodeUsage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 使用记录ID
     */
    @TableId(value = "usage_id", type = IdType.AUTO)
    private Long usageId;

    /**
     * 邀请码ID
     */
    private Long codeId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 使用者用户ID
     */
    private Long userId;

    /**
     * 全局用户ID
     */
    private String globalUserId;

    /**
     * 使用时间
     */
    private Date useTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 状态（0成功 1失败 2待审核）
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime;


}
