package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 管理员管理请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "管理员管理请求参数", description = "用于添加或移除群管理员的请求参数")
public class AdminManageRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要管理的群组ID")
    private Long groupId;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @ApiModelProperty(value = "用户ID列表", required = true, example = "[2,3,4]", notes = "要设置或移除管理员权限的用户ID列表")
    private List<Long> userIds;
} 