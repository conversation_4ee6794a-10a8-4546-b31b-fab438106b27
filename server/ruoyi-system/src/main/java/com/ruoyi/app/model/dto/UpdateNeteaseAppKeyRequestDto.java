package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新网易云信AppKey请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "更新网易云信AppKey请求参数", description = "用于更新网易云信AppKey的请求参数")
public class UpdateNeteaseAppKeyRequestDto {

    /**
     * 新的AppKey
     */
    @NotBlank(message = "AppKey不能为空")
    @Size(max = 100, message = "AppKey长度不能超过100个字符")
    @ApiModelProperty(value = "新的AppKey", required = true, example = "new_app_key_value", notes = "网易云信新的AppKey值")
    private String appKey;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注", example = "更新AppKey原因", notes = "更新备注信息")
    private String remark;
} 