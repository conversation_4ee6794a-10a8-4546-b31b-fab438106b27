package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 配置导入请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "配置导入请求参数", description = "用于导入配置文件的请求参数")
public class ConfigImportRequestDto {

    /**
     * 配置文件内容
     */
    @NotBlank(message = "配置文件内容不能为空")
    @ApiModelProperty(value = "配置文件内容", required = true, example = "[{\"configKey\":\"test.key\",\"configValue\":\"test.value\"}]", notes = "JSON格式的配置文件内容")
    private String configContent;

    /**
     * 是否覆盖现有配置
     */
    @ApiModelProperty(value = "是否覆盖现有配置", example = "false", notes = "遇到重复配置键时是否覆盖现有配置")
    private Boolean overwrite;

    /**
     * 导入描述
     */
    @ApiModelProperty(value = "导入描述", example = "批量导入配置", notes = "本次导入的描述信息")
    private String description;
} 