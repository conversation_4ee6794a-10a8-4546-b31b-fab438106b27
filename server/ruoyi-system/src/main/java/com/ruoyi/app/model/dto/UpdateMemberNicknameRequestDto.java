package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 修改成员昵称请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "修改成员昵称请求参数", description = "用于修改群成员昵称的请求参数")
public class UpdateMemberNicknameRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true, example = "2", notes = "要修改昵称的用户ID")
    private Long userId;

    /**
     * 新昵称
     */
    @NotBlank(message = "新昵称不能为空")
    @ApiModelProperty(value = "新昵称", required = true, example = "张三丰", notes = "用户在群内的新昵称")
    private String newNickname;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1", notes = "执行操作的用户ID")
    private Long operatorId;
} 