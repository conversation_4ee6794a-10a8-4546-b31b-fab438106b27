package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 群组统计信息响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "群组统计信息响应", description = "群组统计数据响应")
public class GroupStatisticsResponseVo {

    /**
     * 群组ID
     */
    @ApiModelProperty(value = "群组ID", example = "1", notes = "群组唯一标识")
    private Long groupId;

    /**
     * 群组名称
     */
    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "群组名称")
    private String groupName;

    /**
     * 总成员数
     */
    @ApiModelProperty(value = "总成员数", example = "50", notes = "群组当前总成员数")
    private Long totalMembers;

    /**
     * 群主数量
     */
    @ApiModelProperty(value = "群主数量", example = "1", notes = "群主数量，通常为1")
    private Long ownerCount;

    /**
     * 管理员数量
     */
    @ApiModelProperty(value = "管理员数量", example = "3", notes = "群管理员数量")
    private Long adminCount;

    /**
     * 普通成员数量
     */
    @ApiModelProperty(value = "普通成员数量", example = "46", notes = "普通成员数量")
    private Long normalCount;

    /**
     * 最大成员数
     */
    @ApiModelProperty(value = "最大成员数", example = "200", notes = "群组最大成员数量")
    private Integer maxMemberCount;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00", notes = "群组创建时间")
    private Date createTime;
} 