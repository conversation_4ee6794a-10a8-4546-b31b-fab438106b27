package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;

import java.util.List;

/**
 * <p>
 * 用户租户关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface QwxUserTenantService extends IService<QwxUserTenant> {

    /**
     * 查询租户内用户列表
     *
     * @param qwxUserTenant 查询条件
     * @return 用户列表
     */
    List<QwxUserTenant> selectQwxUserTenantList(QwxUserTenant qwxUserTenant);

    /**
     * 查询租户内用户详细信息列表（支持查询条件和分页）
     *
     * @param qwxUserTenant 查询条件
     * @return 用户详细信息列表
     */
    List<QwxUserTenantDetailVo> selectQwxUserTenantDetailList(QwxUserTenant qwxUserTenant);

    /**
     * 查询租户下用户详细信息列表（包含联系方式）
     * 用于群主选择等需要完整用户信息的场景
     *
     * @param tenantId 租户ID
     * @return 用户详细信息列表
     */
    List<QwxUserTenantDetailVo> selectTenantUserDetailList(Long tenantId);
}
