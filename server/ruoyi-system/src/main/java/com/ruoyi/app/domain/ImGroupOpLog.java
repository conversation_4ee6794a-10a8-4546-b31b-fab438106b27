package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 群组操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_group_op_log")
public class ImGroupOpLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Long logId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 群组ID
     */
    private Long groupId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 目标用户ID（如踢人、禁言等操作）
     */
    private Long targetUserId;

    /**
     * 操作类型（CREATE创建 UPDATE更新 TRANSFER转让 DISSOLVE解散 ADD_MEMBER添加成员 REMOVE_MEMBER移除成员 SET_ADMIN设置管理员 REMOVE_ADMIN移除管理员 MUTE禁言 UNMUTE解除禁言）
     */
    private String operationType;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 操作前数据（JSON格式）
     */
    private String beforeData;

    /**
     * 操作后数据（JSON格式）
     */
    private String afterData;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态（0成功 1失败）
     */
    private String status;

    /**
     * 错误信息
     */
    private String errorMsg;
} 