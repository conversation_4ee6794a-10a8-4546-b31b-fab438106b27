package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 好友申请记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_friend_request")
public class ImFriendRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 申请发送者用户ID
     */
    private Long senderUserId;

    /**
     * 申请接收者用户ID
     */
    private Long receiverUserId;

    /**
     * 申请留言
     */
    private String requestMessage;

    /**
     * 申请状态（0待处理 1已同意 2已拒绝 3已过期）
     */
    private String status;

    /**
     * 申请发送时间
     */
    private Date requestTime;

    /**
     * 申请处理时间
     */
    private Date responseTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
