package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 成员权限响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "成员权限响应", description = "群成员权限信息响应")
public class MemberPermissionsResponseVo {

    /**
     * 群组ID
     */
    @ApiModelProperty(value = "群组ID", example = "1", notes = "群组唯一标识")
    private Long groupId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "2", notes = "用户唯一标识")
    private Long userId;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色", example = "1", notes = "角色（0普通成员 1管理员 2群主）")
    private String role;

    /**
     * 禁言结束时间
     */
    @ApiModelProperty(value = "禁言结束时间", example = "2024-12-31 23:59:59", notes = "禁言结束时间，为空表示未被禁言")
    private Date muteEndTime;

    /**
     * 是否被禁言
     */
    @ApiModelProperty(value = "是否被禁言", example = "false", notes = "当前是否处于禁言状态")
    private Boolean isMuted;

    /**
     * 群内昵称
     */
    @ApiModelProperty(value = "群内昵称", example = "张三", notes = "用户在群内的昵称")
    private String nickname;

    /**
     * 加入时间
     */
    @ApiModelProperty(value = "加入时间", example = "2024-01-01 12:00:00", notes = "加入群组的时间")
    private Date joinTime;
} 