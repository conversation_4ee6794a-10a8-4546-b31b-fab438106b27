package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImGroupStat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组统计表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImGroupStatService extends IService<ImGroupStat> {

    /**
     * 生成群组每日统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param statDate 统计日期
     * @return 是否生成成功
     */
    boolean generateDailyStat(Long tenantId, Long groupId, Date statDate);

    /**
     * 批量生成租户下所有群组的每日统计数据
     * 
     * @param tenantId 租户ID
     * @param statDate 统计日期
     * @return 生成统计的群组数量
     */
    int generateTenantDailyStat(Long tenantId, Date statDate);

    /**
     * 根据群组ID和日期范围查询统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<ImGroupStat> getStatsByGroupIdAndDateRange(Long tenantId, Long groupId, Date startDate, Date endDate);

    /**
     * 根据租户ID和日期查询所有群组统计数据
     * 
     * @param tenantId 租户ID
     * @param statDate 统计日期
     * @return 统计数据列表
     */
    List<ImGroupStat> getStatsByTenantIdAndDate(Long tenantId, Date statDate);

    /**
     * 获取群组最新统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @return 最新统计数据
     */
    ImGroupStat getLatestStatByGroupId(Long tenantId, Long groupId);

    /**
     * 更新群组实时统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param memberCount 成员数量
     * @param onlineCount 在线人数
     * @return 是否更新成功
     */
    boolean updateRealtimeStat(Long tenantId, Long groupId, Integer memberCount, Integer onlineCount);
} 