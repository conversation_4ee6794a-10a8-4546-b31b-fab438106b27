package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量添加成员请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "批量添加成员请求参数", description = "用于批量拉人入群的请求参数")
public class BatchAddMembersRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @ApiModelProperty(value = "用户ID列表", required = true, example = "[2,3,4]", notes = "要添加的用户ID列表")
    private List<Long> userIds;

    /**
     * 邀请人ID
     */
    @ApiModelProperty(value = "邀请人ID", required = true, example = "1", notes = "邀请人的用户ID")
    private Long inviteUserId;

    /**
     * 加入来源
     */
    @ApiModelProperty(value = "加入来源", example = "1", notes = "加入来源（0直接加入 1邀请加入 2扫码加入 3审批加入）")
    private String joinSource;
} 