package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 群组表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("im_group")
public class ImGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 群组ID
     */
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;

    /**
     * 云信群组ID
     */
    private String cloudGroupId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 群类型（1高级群 2超大群）
     */
    private String groupType;

    /**
     * 群组头像
     */
    private String groupAvatar;

    /**
     * 群主ID
     */
    private Long ownerId;

    /**
     * 群介绍
     */
    private String introduction;

    /**
     * 群公告
     */
    private String announcement;

    /**
     * 成员数量
     */
    private Integer memberCount;

    /**
     * 最大成员数
     */
    private Integer maxMemberCount;

    /**
     * 加入方式（0自由加入 1需审批 2禁止加入）
     */
    private String joinMode;

    /**
     * 全员禁言（0否 1是）
     */
    private String muteAll;

    /**
     * 邀请权限（0所有人 1仅群主和管理员）
     */
    private String inviteMode;

    /**
     * 修改群信息权限（0群主和管理员 1所有人）
     */
    private String updateInfoMode;

    /**
     * 扩展字段（JSON格式）
     */
    private String extension;

    /**
     * 状态（0正常 1解散）
     */
    private String status;

    /**
     * 网易云信群组ID
     */
    private String yxGroupId;

    /**
     * 群禁言类型（0允许所有人发言 1全员禁言 2只允许群主和管理员发言）
     */
    private String teamMuteType;

    /**
     * 被邀请人同意方式（0需要同意 1不需要同意）
     */
    private String beInviteMode;

    /**
     * 自定义字段更新权限（0所有人 1群主和管理员 2仅群主）
     */
    private String updateCustomMode;

    /**
     * 自定义字段（JSON格式）
     */
    private String customField;

    /**
     * 服务器自定义字段（JSON格式）
     */
    private String serverCustomField;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;
}
