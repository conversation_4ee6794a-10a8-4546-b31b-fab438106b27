package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 更新用户在线状态DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel("更新在线状态请求参数")
public class UpdateOnlineStatusDto {

    @ApiModelProperty(value = "在线状态：0离线 1在线 2隐身", required = true)
    @NotNull(message = "在线状态不能为空")
    private Integer onlineStatus;
} 