package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImYxaccountMapping;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 云信账号映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface ImYxaccountMappingMapper extends BaseMapper<ImYxaccountMapping> {

    /**
     * 根据全局用户ID和租户ID查询云信账号信息
     * 
     * @param globalUserId 全局用户ID
     * @param tenantId 租户ID
     * @return 云信账号映射信息
     */
    ImYxaccountMapping selectByGlobalUserIdAndTenantId(@Param("globalUserId") String globalUserId, @Param("tenantId") Long tenantId);
}
