package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.QwxUserTenant;
import com.ruoyi.app.mapper.QwxUserTenantMapper;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import com.ruoyi.app.service.QwxUserTenantService;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户租户关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Service
public class QwxUserTenantServiceImpl extends ServiceImpl<QwxUserTenantMapper, QwxUserTenant> implements QwxUserTenantService {

    @Override
    public List<QwxUserTenant> selectQwxUserTenantList(QwxUserTenant qwxUserTenant) {
        LambdaQueryWrapper<QwxUserTenant> queryWrapper = Wrappers.lambdaQuery();
        
        if (qwxUserTenant.getTenantId() != null) {
            queryWrapper.eq(QwxUserTenant::getTenantId, qwxUserTenant.getTenantId());
        }
        if (StringUtils.isNotEmpty(qwxUserTenant.getNickName())) {
            queryWrapper.like(QwxUserTenant::getNickName, qwxUserTenant.getNickName());
        }
        if (StringUtils.isNotEmpty(qwxUserTenant.getPosition())) {
            queryWrapper.like(QwxUserTenant::getPosition, qwxUserTenant.getPosition());
        }
        if (StringUtils.isNotEmpty(qwxUserTenant.getSignature())) {
            queryWrapper.like(QwxUserTenant::getSignature, qwxUserTenant.getSignature());
        }
        if (StringUtils.isNotEmpty(qwxUserTenant.getStatus())) {
            queryWrapper.eq(QwxUserTenant::getStatus, qwxUserTenant.getStatus());
        }
        
        // 默认查询未退出的用户
        queryWrapper.eq(QwxUserTenant::getIsQuit, "0");
        
        // 按管理员优先，然后按加入时间倒序排列
        queryWrapper.orderByDesc(QwxUserTenant::getIsAdmin)
                   .orderByDesc(QwxUserTenant::getJoinTime);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<QwxUserTenantDetailVo> selectQwxUserTenantDetailList(QwxUserTenant qwxUserTenant) {
        // 使用Mapper原生SQL查询，关联qwx_user_tenant和qwx_global_user表
        // 支持查询条件和分页
        return this.baseMapper.selectTenantUserDetailListWithConditions(qwxUserTenant);
    }

    @Override
    public List<QwxUserTenantDetailVo> selectTenantUserDetailList(Long tenantId) {
        // 使用Mapper原生SQL查询，关联qwx_user_tenant和qwx_global_user表
        // 只查询正常状态的用户
        return this.baseMapper.selectTenantUserDetailList(tenantId, "0");
    }
}
