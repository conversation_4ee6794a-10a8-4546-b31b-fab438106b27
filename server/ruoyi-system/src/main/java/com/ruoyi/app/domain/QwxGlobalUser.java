package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 全局用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxGlobalUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 全局用户ID
     */
    @TableId(type = IdType.INPUT)
    private String globalUserId;

    /**
     * 用户账号（手机号或邮箱）
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号码
     */
    @TableField("phone")
    private String phone;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 是否完善信息（0未完善 1已完善）
     */
    private String isPerfectInfo;

    /**
     * 注册类型（phone手机号 email邮箱）
     */
    private String registerType;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    private String lastLoginIp;

    /**
     * 账号状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 微信openid
     */
    private String wxOpenid;

    /**
     * 微信unionid
     */
    private String wxUnionid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


}
