package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * IM配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImConfigMapper extends BaseMapper<ImConfig> {

    /**
     * 根据租户ID和配置键查询配置
     * 
     * @param tenantId 租户ID
     * @param configKey 配置键
     * @return 配置信息
     */
    ImConfig selectByTenantIdAndKey(@Param("tenantId") Long tenantId, 
                                    @Param("configKey") String configKey);

    /**
     * 根据租户ID查询所有配置
     * 
     * @param tenantId 租户ID
     * @return 配置列表
     */
    List<ImConfig> selectByTenantId(@Param("tenantId") Long tenantId);

    /**
     * 查询系统配置
     * 
     * @return 系统配置列表
     */
    List<ImConfig> selectSystemConfigs();

    /**
     * 根据配置类型查询配置
     * 
     * @param tenantId 租户ID
     * @param configType 配置类型
     * @return 配置列表
     */
    List<ImConfig> selectByConfigType(@Param("tenantId") Long tenantId, 
                                      @Param("configType") String configType);
} 