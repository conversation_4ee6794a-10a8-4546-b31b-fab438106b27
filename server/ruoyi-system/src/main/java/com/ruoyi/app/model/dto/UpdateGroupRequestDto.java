package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 更新群组请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "更新群组请求参数", description = "用于更新群组信息的请求参数")
public class UpdateGroupRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要更新的群组ID")
    private Long groupId;

    /**
     * 群组名称
     */
    @Size(max = 50, message = "群组名称长度不能超过50个字符")
    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "群组名称最大长度50字符")
    private String groupName;

    /**
     * 群组头像
     */
    @ApiModelProperty(value = "群组头像", example = "https://example.com/avatar.jpg", notes = "群组头像URL")
    private String groupAvatar;

    /**
     * 群介绍
     */
    @Size(max = 200, message = "群介绍长度不能超过200个字符")
    @ApiModelProperty(value = "群介绍", example = "这是一个技术交流群", notes = "群介绍最大长度200字符")
    private String introduction;

    /**
     * 群公告
     */
    @Size(max = 500, message = "群公告长度不能超过500个字符")
    @ApiModelProperty(value = "群公告", example = "欢迎大家加入技术交流群，请文明讨论", notes = "群公告最大长度500字符")
    private String announcement;

    /**
     * 加入方式（0自由加入 1需审批 2禁止加入）
     */
    @ApiModelProperty(value = "加入方式", example = "1", notes = "0自由加入 1需审批 2禁止加入", allowableValues = "0,1,2")
    private String joinMode;

    /**
     * 邀请权限（0所有人 1仅群主和管理员）
     */
    @ApiModelProperty(value = "邀请权限", example = "1", notes = "0所有人 1仅群主和管理员", allowableValues = "0,1")
    private String inviteMode;

    /**
     * 修改群信息权限（0所有人 1仅群主和管理员）
     */
    @ApiModelProperty(value = "修改群信息权限", example = "1", notes = "0所有人 1仅群主和管理员", allowableValues = "0,1")
    private String updateInfoMode;

    /**
     * 被邀请人同意方式（0需要同意 1不需要同意）
     */
    @ApiModelProperty(value = "被邀请人同意方式", example = "0", notes = "0需要同意 1不需要同意", allowableValues = "0,1")
    private String beInviteMode;

    /**
     * 全员禁言（false关闭 true开启）
     */
    @ApiModelProperty(value = "全员禁言", example = "false", notes = "是否开启全员禁言，false关闭 true开启")
    private Boolean muteAll;

    /**
     * 扩展字段（JSON格式）
     */
    @ApiModelProperty(value = "扩展字段", example = "{\"key\":\"value\"}", notes = "JSON格式的扩展字段")
    private String extension;

    /**
     * 自定义字段（JSON格式）
     */
    @ApiModelProperty(value = "自定义字段", example = "{\"customKey\":\"customValue\"}", notes = "JSON格式的自定义字段")
    private String customField;
} 