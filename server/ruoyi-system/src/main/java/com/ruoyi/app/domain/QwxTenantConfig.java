package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户配置表 qwx_tenant_config
 * 
 * <AUTHOR>
 */
@Data
@TableName("qwx_tenant_config")
public class QwxTenantConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    /** 租户ID */
    @TableField("tenant_id")
    private Long tenantId;

    /** 配置键名 */
    @TableField("config_key")
    private String configKey;

    /** 配置值 */
    @TableField("config_value")
    private String configValue;

    /** 配置类型（text文本、number数字、boolean布尔、select选择、image图片） */
    @TableField("config_type")
    private String configType;

    /** 是否系统默认（Y是 N否） */
    @TableField("is_default")
    private String isDefault;

    /** 配置名称 */
    @TableField("config_name")
    private String configName;

    /** 配置描述 */
    @TableField("config_desc")
    private String configDesc;

    /** 可选项（适用于select类型，JSON格式） */
    @TableField("options")
    private String options;

    /** 显示顺序 */
    @TableField("order_num")
    private Integer orderNum;

    /** 状态（0正常 1停用） */
    @TableField("status")
    private String status;
    
    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;
    
    /** 更新时间 */
    @TableField("update_time")
    private Date updateTime;
    
    /** 创建者 */
    @TableField("create_by")
    private String createBy;
    
    /** 更新者 */
    @TableField("update_by")
    private String updateBy;
    
    /** 备注 */
    @TableField("remark")
    private String remark;
} 