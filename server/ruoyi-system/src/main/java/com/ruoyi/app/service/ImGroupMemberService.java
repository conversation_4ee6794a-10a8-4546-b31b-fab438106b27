package com.ruoyi.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.MemberPermissionsResponseVo;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;

import java.util.List;

/**
 * <p>
 * 群组成员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
public interface ImGroupMemberService extends IService<ImGroupMember> {

    /**
     * 查询群成员列表
     *
     * @param groupId 群组ID
     * @param keyword 关键词（昵称）
     * @param role 角色
     * @param muteStatus 禁言状态
     * @return 群成员列表
     */
    List<ImGroupMember> selectMemberList(Long groupId, String keyword, String role, String muteStatus);

    /**
     * 查询群成员列表（包含用户详细信息）
     *
     * @param groupId 群组ID
     * @param keyword 关键词（昵称、用户名、手机号、邮箱）
     * @param role 角色
     * @return 群成员详细信息列表
     */
    List<QwxUserTenantDetailVo> selectMemberListWithUserDetails(Long groupId, String keyword, String role);

    /**
     * 获取群成员详细信息
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return 群成员信息
     */
    ImGroupMember getMemberInfo(Long groupId, Long userId);

    /**
     * 添加单个成员
     *
     * @param addMemberRequest 添加成员请求
     * @return 操作结果
     */
    int addMember(AddMemberRequestDto addMemberRequest);

    /**
     * 批量添加成员
     *
     * @param batchAddRequest 批量添加请求
     * @return 操作结果
     */
    int batchAddMembers(BatchAddMembersRequestDto batchAddRequest);

    /**
     * 移除单个成员
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @param leaveReason 移除原因
     * @return 操作结果
     */
    int removeMember(Long groupId, Long userId, Long operatorId, String leaveReason);

    /**
     * 批量移除成员
     *
     * @param batchRemoveRequest 批量移除请求
     * @return 操作结果
     */
    int batchRemoveMembers(BatchRemoveMembersRequestDto batchRemoveRequest);

    /**
     * 设置成员禁言
     *
     * @param muteRequest 禁言请求
     * @return 操作结果
     */
    int muteMember(MuteMemberRequestDto muteRequest);

    /**
     * 取消成员禁言
     *
     * @param unmuteRequest 取消禁言请求
     * @return 操作结果
     */
    int unmuteMember(UnmuteMemberRequestDto unmuteRequest);

    /**
     * 批量设置成员禁言
     *
     * @param batchMuteRequest 批量禁言请求
     * @return 操作结果
     */
    int batchMuteMembers(BatchMuteMembersRequestDto batchMuteRequest);

    /**
     * 设置成员为管理员
     *
     * @param setAdminRequest 设置管理员请求
     * @return 操作结果
     */
    int setMemberAsAdmin(SetMemberAdminRequestDto setAdminRequest);

    /**
     * 取消成员管理员身份
     *
     * @param unsetAdminRequest 取消管理员请求
     * @return 操作结果
     */
    int unsetMemberAsAdmin(SetMemberAdminRequestDto unsetAdminRequest);

    /**
     * 修改成员在群昵称
     *
     * @param nicknameRequest 昵称修改请求
     * @return 操作结果
     */
    int updateMemberNickname(UpdateMemberNicknameRequestDto nicknameRequest);

    /**
     * 获取成员在群中的权限
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return 成员权限信息
     */
    MemberPermissionsResponseVo getMemberPermissions(Long groupId, Long userId);

    /**
     * 批量更新成员权限
     *
     * @param batchPermissionRequest 批量权限更新请求
     * @return 操作结果
     */
    int batchUpdatePermissions(BatchUpdatePermissionsRequestDto batchPermissionRequest);

    /**
     * 查询成员加入群组的历史记录
     *
     * @param groupId 群组ID
     * @param userId 用户ID
     * @return 成员历史记录
     */
    List<ImGroupMember> getMemberHistory(Long groupId, Long userId);
}
