package com.ruoyi.app.model.vo;

import com.ruoyi.app.domain.ImYxaccountMapping;
import com.ruoyi.app.domain.QwxTenant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * APP用户登录结果信息
 * <AUTHOR>
 * @date 2025/5/12 下午9:30
 */
@Data
@Builder
@ApiModel(value = "登录结果")
public class AppLoginResultVo {
        
    /**
     * 过期时间（秒）
     */
    @ApiModelProperty(value = "过期时间（秒）")
    private Integer expiresIn;
    /**
     * 平台级token
     */
    @ApiModelProperty(value = "平台级token")
    private String token;
    
    /**
     * 刷新令牌
     */
    @ApiModelProperty(value = "刷新令牌")
    private String refreshToken;
    
    /**
     * 全局用户ID
     */
    @ApiModelProperty(value = "全局用户ID")
    private String globalUserId;
    
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String phone;
    
    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱")
    private String email;
    
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;

    /**
     * 注册类型（phone手机号 email邮箱）
     */
    @ApiModelProperty(value = "注册类型")
    private String registerType;
    /**
     * 组织和云信账号信息列表
     */
    @ApiModelProperty(value = "组织和云信账号信息列表")
    private List<AppUserTenantIMVo.TenantIMInfo> tenantInfoList;
} 