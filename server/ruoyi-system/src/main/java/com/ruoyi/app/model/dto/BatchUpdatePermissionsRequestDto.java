package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量更新权限请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "批量更新权限请求参数", description = "用于批量更新群成员权限的请求参数")
public class BatchUpdatePermissionsRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户权限配置列表
     */
    @NotEmpty(message = "用户权限配置列表不能为空")
    @ApiModelProperty(value = "用户权限配置列表", required = true, notes = "用户权限配置列表")
    private List<UserPermissionConfig> userPermissions;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1", notes = "执行操作的用户ID")
    private Long operatorId;

    /**
     * 用户权限配置
     */
    @Data
    @ApiModel(value = "用户权限配置", description = "单个用户的权限配置")
    public static class UserPermissionConfig {

        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        @ApiModelProperty(value = "用户ID", required = true, example = "2", notes = "用户ID")
        private Long userId;

        /**
         * 角色
         */
        @NotNull(message = "角色不能为空")
        @ApiModelProperty(value = "角色", required = true, example = "1", notes = "角色（0普通成员 1管理员 2群主）")
        private String role;
    }
} 