package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImConfig;
import com.ruoyi.app.mapper.ImConfigMapper;
import com.ruoyi.app.service.ImConfigService;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.ConfigResponseVo;
import com.ruoyi.app.model.vo.NeteaseConnectionTestResponseVo;
import com.ruoyi.app.model.vo.SystemConfigResponseVo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * <p>
 * IM配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Service
public class ImConfigServiceImpl extends ServiceImpl<ImConfigMapper, ImConfig> implements ImConfigService {

    @Resource
    private ImConfigMapper imConfigMapper;

    @Autowired
    private ObjectMapper objectMapper;

    // 网易云信配置键常量
    private static final String NETEASE_APP_KEY = "netease.app.key";
    private static final String NETEASE_APP_SECRET = "netease.app.secret";

    @Override
    public String getConfigValue(Long tenantId, String configKey) {
        ImConfig config = imConfigMapper.selectByTenantIdAndKey(tenantId, configKey);
        return config != null ? config.getConfigValue() : null;
    }

    @Override
    public String getConfigValue(Long tenantId, String configKey, String defaultValue) {
        String value = getConfigValue(tenantId, configKey);
        return StringUtils.isNotEmpty(value) ? value : defaultValue;
    }

    @Override
    public List<ImConfig> getConfigsByTenantId(Long tenantId) {
        return imConfigMapper.selectByTenantId(tenantId);
    }

    @Override
    public List<ImConfig> getSystemConfigs() {
        return imConfigMapper.selectSystemConfigs();
    }

    @Override
    public List<ImConfig> getConfigsByType(Long tenantId, String configType) {
        return imConfigMapper.selectByConfigType(tenantId, configType);
    }

    @Override
    public boolean updateConfigValue(Long tenantId, String configKey, String configValue) {
        LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImConfig::getTenantId, tenantId)
                   .eq(ImConfig::getConfigKey, configKey);
        
        ImConfig config = getOne(queryWrapper);
        if (config != null) {
            config.setConfigValue(configValue);
            config.setUpdateTime(new Date());
            return updateById(config);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateConfigs(Long tenantId, List<ImConfig> configs) {
        try {
            for (ImConfig config : configs) {
                if (config.getTenantId() == null) {
                    config.setTenantId(tenantId);
                }
                
                LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ImConfig::getTenantId, config.getTenantId())
                           .eq(ImConfig::getConfigKey, config.getConfigKey());
                
                ImConfig existingConfig = getOne(queryWrapper);
                if (existingConfig != null) {
                    config.setConfigId(existingConfig.getConfigId());
                    config.setUpdateTime(new Date());
                    updateById(config);
                } else {
                    config.setCreateTime(new Date());
                    save(config);
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initTenantConfigs(Long tenantId) {
        try {
            // 查询系统配置
            List<ImConfig> systemConfigs = getSystemConfigs();
            
            for (ImConfig systemConfig : systemConfigs) {
                // 检查租户是否已有该配置
                ImConfig existingConfig = imConfigMapper.selectByTenantIdAndKey(tenantId, systemConfig.getConfigKey());
                
                if (existingConfig == null) {
                    // 复制系统配置为租户配置
                    ImConfig tenantConfig = new ImConfig();
                    tenantConfig.setTenantId(tenantId);
                    tenantConfig.setConfigKey(systemConfig.getConfigKey());
                    tenantConfig.setConfigValue(systemConfig.getConfigValue());
                    tenantConfig.setConfigName(systemConfig.getConfigName());
                    tenantConfig.setConfigDesc(systemConfig.getConfigDesc());
                    tenantConfig.setConfigType(systemConfig.getConfigType());
                    tenantConfig.setIsEncrypted(systemConfig.getIsEncrypted());
                    tenantConfig.setIsSystem("0"); // 不是系统配置
                    tenantConfig.setOrderNum(systemConfig.getOrderNum());
                    tenantConfig.setStatus("0");
                    tenantConfig.setCreateTime(new Date());
                    
                    save(tenantConfig);
                }
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetTenantConfigs(Long tenantId) {
        try {
            // 删除租户现有配置
            LambdaQueryWrapper<ImConfig> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(ImConfig::getTenantId, tenantId)
                        .eq(ImConfig::getIsSystem, "0");
            remove(deleteWrapper);
            
            // 重新初始化配置
            return initTenantConfigs(tenantId);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public List<ImConfig> selectConfigList(ConfigQueryRequestDto queryRequest) {
        LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 获取当前租户ID，暂时使用默认值0（系统配置）
        Long tenantId = 0L; // 默认查询系统配置
        queryWrapper.eq(ImConfig::getTenantId, tenantId);
        
        if (StringUtils.isNotBlank(queryRequest.getConfigType())) {
            queryWrapper.eq(ImConfig::getConfigType, queryRequest.getConfigType());
        }
        if (StringUtils.isNotBlank(queryRequest.getConfigKey())) {
            queryWrapper.like(ImConfig::getConfigKey, queryRequest.getConfigKey());
        }
        if (StringUtils.isNotBlank(queryRequest.getStatus())) {
            queryWrapper.eq(ImConfig::getStatus, queryRequest.getStatus());
        }
        if (StringUtils.isNotBlank(queryRequest.getConfigName())) {
            queryWrapper.like(ImConfig::getConfigName, queryRequest.getConfigName());
        }
        if (StringUtils.isNotBlank(queryRequest.getIsSystem())) {
            queryWrapper.eq(ImConfig::getIsSystem, queryRequest.getIsSystem());
        }
        
        queryWrapper.orderByAsc(ImConfig::getOrderNum, ImConfig::getConfigId);
        
        return list(queryWrapper);
    }

    @Override
    public ConfigResponseVo getConfigByKey(String configKey) {
        Long tenantId = 0L; // 默认查询系统配置
        ImConfig config = imConfigMapper.selectByTenantIdAndKey(tenantId, configKey);
        
        if (config == null) {
            return null;
        }
        
        ConfigResponseVo responseVo = new ConfigResponseVo();
        BeanUtils.copyBeanProp(responseVo, config);
        
        // 对加密字段进行脱敏处理
        if ("1".equals(config.getIsEncrypted()) && StringUtils.isNotBlank(config.getConfigValue())) {
            responseVo.setConfigValue(maskValue(config.getConfigValue()));
        }
        
        return responseVo;
    }

    @Override
    public String getNeteaseAppKey() {
        Long tenantId = 0L; // 默认查询系统配置
        return getConfigValue(tenantId, NETEASE_APP_KEY);
    }

    @Override
    public String getNeteaseAppSecretMasked() {
        Long tenantId = 0L; // 默认查询系统配置
        String appSecret = getConfigValue(tenantId, NETEASE_APP_SECRET);
        return maskValue(appSecret);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertConfig(CreateConfigRequestDto request) {
        Long tenantId = 0L; // 默认使用系统配置租户ID
        String currentUser = SecurityUtils.getUsername();
        
        // 检查配置键是否已存在
        ImConfig existingConfig = imConfigMapper.selectByTenantIdAndKey(tenantId, request.getConfigKey());
        if (existingConfig != null) {
            throw new RuntimeException("配置键已存在: " + request.getConfigKey());
        }
        
        ImConfig config = new ImConfig();
        BeanUtils.copyBeanProp(config, request);
        config.setTenantId(tenantId);
        config.setCreateTime(new Date());
        config.setCreateBy(currentUser);
        
        // 设置默认值
        if (StringUtils.isBlank(config.getConfigType())) {
            config.setConfigType("text");
        }
        if (StringUtils.isBlank(config.getIsEncrypted())) {
            config.setIsEncrypted("0");
        }
        if (StringUtils.isBlank(config.getIsSystem())) {
            config.setIsSystem("0");
        }
        if (StringUtils.isBlank(config.getStatus())) {
            config.setStatus("0");
        }
        if (config.getOrderNum() == null) {
            config.setOrderNum(0);
        }
        
        return save(config) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateConfig(UpdateConfigRequestDto request) {
        String currentUser = SecurityUtils.getUsername();
        
        ImConfig existingConfig = getById(request.getConfigId());
        if (existingConfig == null) {
            throw new RuntimeException("配置不存在");
        }
        
        ImConfig config = new ImConfig();
        config.setConfigId(request.getConfigId());
        config.setConfigValue(request.getConfigValue());
        config.setConfigName(request.getConfigName());
        config.setConfigDesc(request.getConfigDesc());
        config.setConfigType(request.getConfigType());
        config.setIsEncrypted(request.getIsEncrypted());
        config.setOrderNum(request.getOrderNum());
        config.setStatus(request.getStatus());
        config.setRemark(request.getRemark());
        config.setUpdateTime(new Date());
        config.setUpdateBy(currentUser);
        
        return updateById(config) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNeteaseAppKey(UpdateNeteaseAppKeyRequestDto request) {
        Long tenantId = 0L; // 使用系统配置租户ID
        String currentUser = SecurityUtils.getUsername();
        
        LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImConfig::getTenantId, tenantId)
                   .eq(ImConfig::getConfigKey, NETEASE_APP_KEY);
        
        ImConfig config = getOne(queryWrapper);
        if (config == null) {
            // 如果不存在则创建
            config = new ImConfig();
            config.setTenantId(tenantId);
            config.setConfigKey(NETEASE_APP_KEY);
            config.setConfigName("网易云信AppKey");
            config.setConfigDesc("网易云信应用的AppKey");
            config.setConfigType("text");
            config.setIsEncrypted("0");
            config.setIsSystem("1");
            config.setOrderNum(1);
            config.setStatus("0");
            config.setCreateTime(new Date());
            config.setCreateBy(currentUser);
        }
        
        config.setConfigValue(request.getAppKey());
        config.setRemark(request.getRemark());
        config.setUpdateTime(new Date());
        config.setUpdateBy(currentUser);
        
        return config.getConfigId() == null ? (save(config) ? 1 : 0) : (updateById(config) ? 1 : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateNeteaseAppSecret(UpdateNeteaseAppSecretRequestDto request) {
        Long tenantId = 0L; // 使用系统配置租户ID
        String currentUser = SecurityUtils.getUsername();
        
        LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ImConfig::getTenantId, tenantId)
                   .eq(ImConfig::getConfigKey, NETEASE_APP_SECRET);
        
        ImConfig config = getOne(queryWrapper);
        if (config == null) {
            // 如果不存在则创建
            config = new ImConfig();
            config.setTenantId(tenantId);
            config.setConfigKey(NETEASE_APP_SECRET);
            config.setConfigName("网易云信AppSecret");
            config.setConfigDesc("网易云信应用的AppSecret");
            config.setConfigType("text");
            config.setIsEncrypted("1");
            config.setIsSystem("1");
            config.setOrderNum(2);
            config.setStatus("0");
            config.setCreateTime(new Date());
            config.setCreateBy(currentUser);
        }
        
        config.setConfigValue(request.getAppSecret());
        config.setRemark(request.getRemark());
        config.setUpdateTime(new Date());
        config.setUpdateBy(currentUser);
        
        return config.getConfigId() == null ? (save(config) ? 1 : 0) : (updateById(config) ? 1 : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteConfigByIds(Long[] configIds) {
        List<Long> idList = Arrays.asList(configIds);
        
        // 检查是否有系统配置
        LambdaQueryWrapper<ImConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ImConfig::getConfigId, idList)
                   .eq(ImConfig::getIsSystem, "1");
        
        List<ImConfig> systemConfigs = list(queryWrapper);
        if (!systemConfigs.isEmpty()) {
            String systemConfigNames = systemConfigs.stream()
                    .map(ImConfig::getConfigName)
                    .collect(Collectors.joining(", "));
            throw new RuntimeException("不能删除系统配置: " + systemConfigNames);
        }
        
        return removeByIds(idList) ? idList.size() : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int switchConfig(ConfigSwitchRequestDto request) {
        String currentUser = SecurityUtils.getUsername();
        
        ImConfig config = getById(request.getConfigId());
        if (config == null) {
            throw new RuntimeException("配置不存在");
        }
        
        config.setStatus(request.getStatus());
        config.setUpdateTime(new Date());
        config.setUpdateBy(currentUser);
        
        return updateById(config) ? 1 : 0;
    }

    @Override
    public NeteaseConnectionTestResponseVo testNeteaseConnection() {
        Long tenantId = 0L; // 使用系统配置租户ID
        
        String appKey = getConfigValue(tenantId, NETEASE_APP_KEY);
        String appSecret = getConfigValue(tenantId, NETEASE_APP_SECRET);
        
        NeteaseConnectionTestResponseVo response = new NeteaseConnectionTestResponseVo();
        
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(appSecret)) {
            response.setSuccess(false);
            response.setMessage("网易云信配置不完整");
            response.setErrorCode("CONFIG_INCOMPLETE");
            response.setErrorDetail("AppKey或AppSecret未配置");
            return response;
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // TODO: 这里应该调用网易云信API进行实际测试
            // 目前先返回成功，待集成网易云信SDK后实现
            Thread.sleep(100); // 模拟网络请求
            
            long endTime = System.currentTimeMillis();
            
            response.setSuccess(true);
            response.setMessage("连接测试成功");
            response.setResponseTime(endTime - startTime);
            response.setErrorCode("200");
            
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("连接测试失败");
            response.setErrorCode("CONNECTION_FAILED");
            response.setErrorDetail(e.getMessage());
        }
        
        return response;
    }

    @Override
    public void refreshCache() {
        // TODO: 实现配置缓存刷新逻辑
        // 可以结合Redis缓存实现
    }

    @Override
    public SystemConfigResponseVo getSystemConfig() {
        Long tenantId = 0L; // 使用系统配置租户ID
        List<ImConfig> configs = getConfigsByTenantId(tenantId);
        
        SystemConfigResponseVo response = new SystemConfigResponseVo();
        
        Map<String, Object> neteaseConfig = new HashMap<>();
        Map<String, Object> systemConfig = new HashMap<>();
        Map<String, Object> featureConfig = new HashMap<>();
        Map<String, Object> securityConfig = new HashMap<>();
        
        for (ImConfig config : configs) {
            String value = "1".equals(config.getIsEncrypted()) ? maskValue(config.getConfigValue()) : config.getConfigValue();
            
            if (config.getConfigKey().startsWith("netease.")) {
                neteaseConfig.put(config.getConfigKey(), value);
            } else if (config.getConfigKey().startsWith("system.")) {
                systemConfig.put(config.getConfigKey(), value);
            } else if (config.getConfigKey().startsWith("feature.")) {
                featureConfig.put(config.getConfigKey(), value);
            } else if (config.getConfigKey().startsWith("security.")) {
                securityConfig.put(config.getConfigKey(), value);
            }
        }
        
        response.setNeteaseConfig(neteaseConfig);
        response.setSystemConfig(systemConfig);
        response.setFeatureConfig(featureConfig);
        response.setSecurityConfig(securityConfig);
        
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importConfig(ConfigImportRequestDto request) {
        try {
            Long tenantId = 0L; // 使用系统配置租户ID
            String currentUser = SecurityUtils.getUsername();
            
            // 解析JSON配置内容
            List<Map<String, Object>> configList = objectMapper.readValue(
                    request.getConfigContent(), 
                    new TypeReference<List<Map<String, Object>>>() {}
            );
            
            int successCount = 0;
            int skipCount = 0;
            int errorCount = 0;
            List<String> errorMessages = new ArrayList<>();
            
            for (Map<String, Object> configMap : configList) {
                try {
                    String configKey = (String) configMap.get("configKey");
                    String configValue = (String) configMap.get("configValue");
                    String configName = (String) configMap.get("configName");
                    
                    if (StringUtils.isBlank(configKey)) {
                        errorCount++;
                        errorMessages.add("配置键不能为空");
                        continue;
                    }
                    
                    // 检查是否已存在
                    ImConfig existingConfig = imConfigMapper.selectByTenantIdAndKey(tenantId, configKey);
                    
                    if (existingConfig != null && !Boolean.TRUE.equals(request.getOverwrite())) {
                        skipCount++;
                        continue;
                    }
                    
                    ImConfig config = existingConfig != null ? existingConfig : new ImConfig();
                    config.setTenantId(tenantId);
                    config.setConfigKey(configKey);
                    config.setConfigValue(configValue);
                    config.setConfigName(StringUtils.isNotBlank(configName) ? configName : configKey);
                    config.setConfigDesc((String) configMap.getOrDefault("configDesc", ""));
                    config.setConfigType((String) configMap.getOrDefault("configType", "text"));
                    config.setIsEncrypted((String) configMap.getOrDefault("isEncrypted", "0"));
                    config.setIsSystem("0");
                    config.setOrderNum((Integer) configMap.getOrDefault("orderNum", 0));
                    config.setStatus((String) configMap.getOrDefault("status", "0"));
                    config.setRemark(request.getDescription());
                    
                    if (existingConfig != null) {
                        config.setUpdateTime(new Date());
                        config.setUpdateBy(currentUser);
                        updateById(config);
                    } else {
                        config.setCreateTime(new Date());
                        config.setCreateBy(currentUser);
                        save(config);
                    }
                    
                    successCount++;
                    
                } catch (Exception e) {
                    errorCount++;
                    errorMessages.add("处理配置失败: " + e.getMessage());
                }
            }
            
            String resultMessage = String.format("导入完成：成功 %d 条，跳过 %d 条，失败 %d 条", 
                    successCount, skipCount, errorCount);
            
            if (!errorMessages.isEmpty()) {
                resultMessage += "。错误详情：" + String.join("; ", errorMessages);
            }
            
            return resultMessage;
            
        } catch (Exception e) {
            throw new RuntimeException("导入配置文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 对敏感信息进行脱敏处理
     */
    private String maskValue(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        
        if (value.length() <= 4) {
            return "****";
        }
        
        return value.substring(0, 2) + "****" + value.substring(value.length() - 2);
    }
} 