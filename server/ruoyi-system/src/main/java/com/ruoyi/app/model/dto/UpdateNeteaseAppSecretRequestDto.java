package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 更新网易云信AppSecret请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "更新网易云信AppSecret请求参数", description = "用于更新网易云信AppSecret的请求参数")
public class UpdateNeteaseAppSecretRequestDto {

    /**
     * 新的AppSecret
     */
    @NotBlank(message = "AppSecret不能为空")
    @Size(max = 200, message = "AppSecret长度不能超过200个字符")
    @ApiModelProperty(value = "新的AppSecret", required = true, example = "new_app_secret_value", notes = "网易云信新的AppSecret值")
    private String appSecret;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @ApiModelProperty(value = "备注", example = "更新AppSecret原因", notes = "更新备注信息")
    private String remark;
} 