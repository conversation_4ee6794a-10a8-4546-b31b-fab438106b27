package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 部署实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxDeployInstance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 实例ID
     */
    @TableId(value = "instance_id", type = IdType.AUTO)
    private Long instanceId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * 实例编码，唯一标识
     */
    private String instanceCode;

    /**
     * 公网IP
     */
    private String publicIp;

    /**
     * 内网IP
     */
    private String privateIp;

    /**
     * 访问域名
     */
    private String domain;

    /**
     * 许可证密钥
     */
    private String licenseKey;

    /**
     * 部署环境(dev开发,test测试,prod生产)
     */
    private String deployEnv;

    /**
     * 最大用户数
     */
    private Integer maxUsers;

    /**
     * 当前版本号
     */
    private String version;

    /**
     * 最近心跳时间
     */
    private Date heartbeatTime;

    /**
     * 心跳间隔(秒)
     */
    private Integer heartbeatInterval;

    /**
     * 状态（0正常 1停用 2离线）
     */
    private String status;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 离线授权天数
     */
    private Integer offlineAuthDays;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;


}
