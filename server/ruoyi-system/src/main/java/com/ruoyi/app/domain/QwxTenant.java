package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.domain.DataScopeCapable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxTenant implements DataScopeCapable, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @TableId(value = "tenant_id", type = IdType.AUTO)
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户编码，唯一标识
     */
    private String tenantCode;

    /**
     * 企业logo
     */
    private String logo;

    /**
     * 行业
     */
    private String industry;

    /**
     * 企业规模
     */
    private String scale;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 联系人
     */
    private String contactUser;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 域名
     */
    private String domain;

    /**
     * 企业介绍
     */
    private String intro;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0存在 2删除）
     */
    private String delFlag;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 账号数量
     */
    private Integer accountCount;

    /**
     * 创建人ID（关联全局用户ID）
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 请求参数 - 用于数据权限过滤
     */
    @TableField(exist = false)
    private Map<String, Object> params;

    @Override
    public Map<String, Object> getParams() {
        if (params == null) {
            params = new HashMap<>();
        }
        return params;
    }

    @Override
    public void setParams(Map<String, Object> params) {
        this.params = params;
    }



}
