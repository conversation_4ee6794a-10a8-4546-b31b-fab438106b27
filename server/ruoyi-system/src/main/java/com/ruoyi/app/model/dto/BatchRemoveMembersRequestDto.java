package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量移除成员请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "批量移除成员请求参数", description = "用于批量移除群成员的请求参数")
public class BatchRemoveMembersRequestDto {

    /**
     * 群组ID
     */
    @NotNull(message = "群组ID不能为空")
    @ApiModelProperty(value = "群组ID", required = true, example = "1", notes = "要操作的群组ID")
    private Long groupId;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    @ApiModelProperty(value = "用户ID列表", required = true, example = "[2,3,4]", notes = "要移除的用户ID列表")
    private List<Long> userIds;

    /**
     * 操作人ID
     */
    @NotNull(message = "操作人ID不能为空")
    @ApiModelProperty(value = "操作人ID", required = true, example = "1", notes = "执行移除操作的用户ID")
    private Long operatorId;

    /**
     * 移除原因
     */
    @ApiModelProperty(value = "移除原因", example = "违反群规", notes = "移除成员的原因")
    private String leaveReason;
} 