package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 系统实例心跳表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxInstanceHeartbeat implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 实例ID
     */
    private Long instanceId;

    /**
     * 心跳时间
     */
    private Date heartbeatTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 系统信息
     */
    private String systemInfo;

    /**
     * CPU使用率(%)
     */
    private BigDecimal cpuUsage;

    /**
     * 内存使用率(%)
     */
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率(%)
     */
    private BigDecimal diskUsage;

    /**
     * 网络速度
     */
    private String networkSpeed;

    /**
     * 活跃用户数
     */
    private Integer activeUsers;

    /**
     * 在线状态（0在线 1离线）
     */
    private String onlineStatus;

    /**
     * 创建时间
     */
    private Date createTime;


}
