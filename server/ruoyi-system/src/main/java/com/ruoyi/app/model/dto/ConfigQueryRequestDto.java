package com.ruoyi.app.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配置查询请求DTO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "配置查询请求参数", description = "用于查询IM配置的请求参数")
public class ConfigQueryRequestDto {

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型", example = "text", notes = "配置类型：text文本 number数字 boolean布尔 json对象")
    private String configType;

    /**
     * 配置键名
     */
    @ApiModelProperty(value = "配置键名", example = "netease.app.key", notes = "配置键名")
    private String configKey;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "0", notes = "状态（0正常 1停用）", allowableValues = "0,1")
    private String status;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称", example = "网易云信AppKey", notes = "配置名称，支持模糊查询")
    private String configName;

    /**
     * 是否系统配置
     */
    @ApiModelProperty(value = "是否系统配置", example = "1", notes = "是否系统配置（0否 1是）", allowableValues = "0,1")
    private String isSystem;
} 