package com.ruoyi.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.app.domain.ImGroupStat;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface ImGroupStatMapper extends BaseMapper<ImGroupStat> {

    /**
     * 根据群组ID和日期范围查询统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据列表
     */
    List<ImGroupStat> selectByGroupIdAndDateRange(@Param("tenantId") Long tenantId, 
                                                   @Param("groupId") Long groupId,
                                                   @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate);

    /**
     * 根据租户ID和日期查询所有群组统计数据
     * 
     * @param tenantId 租户ID
     * @param statDate 统计日期
     * @return 统计数据列表
     */
    List<ImGroupStat> selectByTenantIdAndDate(@Param("tenantId") Long tenantId, 
                                              @Param("statDate") Date statDate);

    /**
     * 获取群组最新统计数据
     * 
     * @param tenantId 租户ID
     * @param groupId 群组ID
     * @return 最新统计数据
     */
    ImGroupStat selectLatestByGroupId(@Param("tenantId") Long tenantId, 
                                      @Param("groupId") Long groupId);
} 