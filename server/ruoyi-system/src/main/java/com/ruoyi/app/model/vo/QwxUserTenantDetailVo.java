package com.ruoyi.app.model.vo;

import com.ruoyi.app.domain.QwxUserTenant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户用户详细信息VO
 * 包含用户基础信息和联系方式
 * 
 * <AUTHOR>
 * @date 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QwxUserTenantDetailVo extends QwxUserTenant {

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", example = "13800138000")
    private String phone;

    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 用户名（从全局用户表获取）
     */
    @ApiModelProperty(value = "用户名", example = "zhangsan")
    private String userName;

} 