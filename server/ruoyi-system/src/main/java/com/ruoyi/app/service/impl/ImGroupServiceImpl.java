package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImGroup;
import com.ruoyi.app.domain.ImGroupMember;
import com.ruoyi.app.mapper.ImGroupMapper;
import com.ruoyi.app.mapper.QwxUserTenantMapper;
import com.ruoyi.app.model.dto.*;
import com.ruoyi.app.model.vo.GroupStatisticsResponseVo;
import com.ruoyi.app.model.vo.QwxUserTenantDetailVo;
import com.ruoyi.app.service.ImGroupMemberService;
import com.ruoyi.app.service.ImGroupService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 群组表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Service
@Slf4j
public class ImGroupServiceImpl extends ServiceImpl<ImGroupMapper, ImGroup> implements ImGroupService {

    @Resource
    private ImGroupMemberService groupMemberService;
    @Resource
    private QwxUserTenantMapper qwxUserTenantMapper;

    @Override
    public List<ImGroup> selectGroupList(String groupName, String groupType, String status, Long tenantId) {
        LambdaQueryWrapper<ImGroup> wrapper = Wrappers.lambdaQuery();

        if (StringUtils.isNotBlank(groupName)) {
            wrapper.like(ImGroup::getGroupName, groupName);
        }
        if (StringUtils.isNotBlank(groupType)) {
            wrapper.eq(ImGroup::getGroupType, groupType);
        }
        if (StringUtils.isNotBlank(status)) {
            wrapper.eq(ImGroup::getStatus, status);
        } else {
            // 默认查询正常状态的群组
            wrapper.eq(ImGroup::getStatus, "0");
        }
        if (tenantId != null) {
            wrapper.eq(ImGroup::getTenantId, tenantId);
        }

        wrapper.orderByDesc(ImGroup::getCreateTime);

        return list(wrapper);
    }

    @Override
    public List<ImGroup> selectGroupByIds(List<Long> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyList();
        }

        return listByIds(groupIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createGroup(CreateGroupRequestDto request) {
        try {
            ImGroup group = new ImGroup();
            group.setGroupName(request.getGroupName());
            group.setGroupType(request.getGroupType());
            group.setGroupAvatar(request.getGroupAvatar());
            group.setIntroduction(request.getIntroduction());
            group.setAnnouncement(request.getAnnouncement());
            group.setMaxMemberCount(request.getMaxMemberCount());
            group.setJoinMode(request.getJoinMode());
            group.setInviteMode(request.getInviteMode());
            group.setUpdateInfoMode(request.getUpdateInfoMode());
            group.setBeInviteMode(request.getBeInviteMode());
            group.setExtension(request.getExtension());
            group.setCustomField(request.getCustomField());
            group.setYxGroupId(request.getYxGroupId()); // 保存网易云信群组ID

            // 使用前端传入的租户ID和群主ID
            group.setOwnerId(request.getOwnerId());
            group.setTenantId(request.getTenantId());
            group.setMemberCount(1); // 群主
            group.setStatus("0"); // 正常状态
            group.setCreateTime(new Date());
            group.setCreateBy(SecurityUtils.getUsername());

            // 设置默认最大成员数
            if (group.getMaxMemberCount() == null) {
                group.setMaxMemberCount("1".equals(group.getGroupType()) ? 200 : 2000);
            }

            // 设置默认属性
            if (StringUtils.isBlank(group.getJoinMode())) {
                group.setJoinMode("1"); // 默认需要审批
            }
            if (StringUtils.isBlank(group.getInviteMode())) {
                group.setInviteMode("1"); // 默认仅群主和管理员可邀请
            }
            if (StringUtils.isBlank(group.getUpdateInfoMode())) {
                group.setUpdateInfoMode("0"); // 默认仅群主和管理员可修改群信息
            }
            if (StringUtils.isBlank(group.getBeInviteMode())) {
                group.setBeInviteMode("0"); // 默认被邀请需要同意
            }

            boolean saved = save(group);

            if (saved && group.getGroupId() != null) {

                QwxUserTenantDetailVo userDetail = qwxUserTenantMapper.getTenantUserDetail(group.getTenantId(), group.getOwnerId());

                // 创建群主成员记录
                ImGroupMember ownerMember = new ImGroupMember();
                ownerMember.setTenantId(group.getTenantId());
                ownerMember.setGroupId(group.getGroupId());
                ownerMember.setUserId(group.getOwnerId());
                ownerMember.setNickname(userDetail.getNickName());
                ownerMember.setRole("2"); // 群主
                ownerMember.setJoinTime(new Date());
                ownerMember.setCreateTime(new Date());
                groupMemberService.save(ownerMember);

                // 添加初始成员
                List<Long> memberIds = request.getMemberIds();
                if (memberIds != null && !memberIds.isEmpty()) {
                    List<ImGroupMember> members = new ArrayList<>();
                    for (Long memberId : memberIds) {
                        if (!memberId.equals(group.getOwnerId())) {
                            QwxUserTenantDetailVo user = qwxUserTenantMapper.getTenantUserDetail(group.getTenantId(), memberId);

                            ImGroupMember member = new ImGroupMember();
                            member.setTenantId(group.getTenantId());
                            member.setGroupId(group.getGroupId());
                            member.setUserId(memberId);
                            member.setNickname(user.getNickName());
                            member.setRole("0"); // 普通成员
                            member.setJoinTime(new Date());
                            member.setCreateTime(new Date());
                            members.add(member);
                        }
                    }
                    if (!members.isEmpty()) {
                        groupMemberService.saveBatch(members);
                        // 更新群成员数
                        group.setMemberCount(1 + members.size());
                        updateById(group);
                    }
                }
            }

            return saved ? 1 : 0;
        } catch (Exception e) {
            log.error("创建群组失败", e);
            throw new RuntimeException("创建群组失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateGroup(UpdateGroupRequestDto request) {
        try {
            Long groupId = request.getGroupId();
            ImGroup existGroup = getById(groupId);
            if (existGroup == null) {
                throw new RuntimeException("群组不存在");
            }

            // 检查权限：只有群主和管理员可以修改群信息
            // 获取的id是管理后台id和群主id不是一套体系,暂时取消限制
            // Long currentUserId = SecurityUtils.getUserId();
            // if (!existGroup.getOwnerId().equals(currentUserId)) {
            //     // 检查是否为管理员
            //     LambdaQueryWrapper<ImGroupMember> memberWrapper = Wrappers.lambdaQuery();
            //     memberWrapper.eq(ImGroupMember::getGroupId, groupId)
            //                .eq(ImGroupMember::getUserId, currentUserId)
            //                .in(ImGroupMember::getRole, Arrays.asList("1", "2")); // 管理员或群主
            //     ImGroupMember member = groupMemberService.getOne(memberWrapper);
            //     if (member == null) {
            //         throw new RuntimeException("无权限修改群组信息");
            //     }
            // }

            ImGroup updateGroup = new ImGroup();
            updateGroup.setGroupId(groupId);
            updateGroup.setGroupName(request.getGroupName());
            updateGroup.setGroupAvatar(request.getGroupAvatar());
            updateGroup.setIntroduction(request.getIntroduction());
            updateGroup.setAnnouncement(request.getAnnouncement());
            updateGroup.setJoinMode(request.getJoinMode());
            updateGroup.setInviteMode(request.getInviteMode());
            updateGroup.setUpdateInfoMode(request.getUpdateInfoMode());
            updateGroup.setBeInviteMode(request.getBeInviteMode());
            // 处理全员禁言设置
            if (request.getMuteAll() != null) {
                updateGroup.setMuteAll(request.getMuteAll() ? "1" : "0");
            }
            updateGroup.setExtension(request.getExtension());
            updateGroup.setCustomField(request.getCustomField());
            updateGroup.setUpdateTime(new Date());
            updateGroup.setUpdateBy(SecurityUtils.getUsername());

            return updateById(updateGroup) ? 1 : 0;
        } catch (Exception e) {
            log.error("更新群组信息失败", e);
            throw new RuntimeException("更新群组信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int transferOwner(TransferOwnerRequestDto request) {
        try {
            Long groupId = request.getGroupId();
            Long newOwnerId = request.getNewOwnerId();
            String leaveGroup = request.getLeaveGroup();

            ImGroup group = getById(groupId);
            if (group == null) {
                throw new RuntimeException("群组不存在");
            }

            // 检查权限：只有群主可以转让
            // Long currentUserId = SecurityUtils.getUserId();
            // if (!group.getOwnerId()
            //         .equals(currentUserId)) {
            //     throw new RuntimeException("只有群主可以转让群主权限");
            // }


            // 更新群主
            ImGroup updateGroup = new ImGroup();
            updateGroup.setGroupId(groupId);
            updateGroup.setOwnerId(newOwnerId);
            updateGroup.setUpdateTime(new Date());
            updateGroup.setUpdateBy(SecurityUtils.getUsername());

            // 更新群主成员角色
            ImGroupMember updateNewOwner = new ImGroupMember();
            updateNewOwner.setGroupId(groupId);
            updateNewOwner.setUserId(newOwnerId);
            updateNewOwner.setRole("2"); // 群主

            LambdaQueryWrapper<ImGroupMember> newOwnerWrapper = Wrappers.lambdaQuery();
            newOwnerWrapper.eq(ImGroupMember::getGroupId, groupId)
                    .eq(ImGroupMember::getUserId, newOwnerId);
            groupMemberService.update(updateNewOwner, newOwnerWrapper);

            // 原群主的处理
            if ("1".equals(leaveGroup)) {
                // 原群主退出群组 - 更新状态而不是删除记录
                ImGroupMember updateOldOwner = new ImGroupMember();
                updateOldOwner.setRole("0"); // 普通成员
                updateOldOwner.setStatus("1"); // 设置为已退出
                updateOldOwner.setLeaveTime(new Date()); // 记录离开时间
                updateOldOwner.setLeaveReason("转让群主后退出"); // 记录离开原因
                updateOldOwner.setUpdateTime(new Date());

                LambdaQueryWrapper<ImGroupMember> oldOwnerWrapper = Wrappers.lambdaQuery();
                oldOwnerWrapper.eq(ImGroupMember::getGroupId, groupId)
                        .eq(ImGroupMember::getUserId, request.getOldOwnerId());
                groupMemberService.update(updateOldOwner, oldOwnerWrapper);

                // 减少群成员数量
                group.setMemberCount(group.getMemberCount() - 1);
                updateGroup.setMemberCount(group.getMemberCount());
            } else {
                // 原群主变为普通成员
                ImGroupMember updateOldOwner = new ImGroupMember();
                updateOldOwner.setRole("0"); // 普通成员

                LambdaQueryWrapper<ImGroupMember> oldOwnerWrapper = Wrappers.lambdaQuery();
                oldOwnerWrapper.eq(ImGroupMember::getGroupId, groupId)
                        .eq(ImGroupMember::getUserId, request.getOldOwnerId());
                groupMemberService.update(updateOldOwner, oldOwnerWrapper);
            }

            return updateById(updateGroup) ? 1 : 0;
        } catch (Exception e) {
            log.error("转让群主失败", e);
            throw new RuntimeException("转让群主失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addAdmin(AdminManageRequestDto request) {
        try {
            Long groupId = request.getGroupId();
            List<Long> userIds = request.getUserIds();

            ImGroup group = getById(groupId);
            if (group == null) {
                throw new RuntimeException("群组不存在");
            }

            // 检查权限：只有群主可以添加管理员
            Long currentUserId = SecurityUtils.getUserId();
            if (!group.getOwnerId()
                    .equals(currentUserId)) {
                throw new RuntimeException("只有群主可以添加管理员");
            }

            // 批量更新成员角色为管理员
            for (Long userId : userIds) {
                LambdaQueryWrapper<ImGroupMember> memberWrapper = Wrappers.lambdaQuery();
                memberWrapper.eq(ImGroupMember::getGroupId, groupId)
                        .eq(ImGroupMember::getUserId, userId);
                ImGroupMember member = groupMemberService.getOne(memberWrapper);

                if (member == null) {
                    log.warn("用户{}不是群{}的成员，跳过", userId, groupId);
                    continue;
                }

                if ("2".equals(member.getRole())) {
                    log.warn("用户{}已经是群{}的群主，跳过", userId, groupId);
                    continue;
                }

                // 更新为管理员
                ImGroupMember updateMember = new ImGroupMember();
                updateMember.setRole("1"); // 管理员
                groupMemberService.update(updateMember, memberWrapper);
            }

            return 1;
        } catch (Exception e) {
            log.error("添加管理员失败", e);
            throw new RuntimeException("添加管理员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int removeAdmin(AdminManageRequestDto request) {
        try {
            Long groupId = request.getGroupId();
            List<Long> userIds = request.getUserIds();

            ImGroup group = getById(groupId);
            if (group == null) {
                throw new RuntimeException("群组不存在");
            }

            // 检查权限：只有群主可以移除管理员
            Long currentUserId = SecurityUtils.getUserId();
            if (!group.getOwnerId()
                    .equals(currentUserId)) {
                throw new RuntimeException("只有群主可以移除管理员");
            }

            // 批量更新管理员角色为普通成员
            for (Long userId : userIds) {
                if (userId.equals(currentUserId)) {
                    log.warn("不能移除自己的管理员权限，跳过");
                    continue;
                }

                LambdaQueryWrapper<ImGroupMember> memberWrapper = Wrappers.lambdaQuery();
                memberWrapper.eq(ImGroupMember::getGroupId, groupId)
                        .eq(ImGroupMember::getUserId, userId);
                ImGroupMember member = groupMemberService.getOne(memberWrapper);

                if (member == null) {
                    log.warn("用户{}不是群{}的成员，跳过", userId, groupId);
                    continue;
                }

                if (!"1".equals(member.getRole())) {
                    log.warn("用户{}不是群{}的管理员，跳过", userId, groupId);
                    continue;
                }

                // 更新为普通成员
                ImGroupMember updateMember = new ImGroupMember();
                updateMember.setRole("0"); // 普通成员
                groupMemberService.update(updateMember, memberWrapper);
            }

            return 1;
        } catch (Exception e) {
            log.error("移除管理员失败", e);
            throw new RuntimeException("移除管理员失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int dissolveGroup(Long groupId) {
        try {
            ImGroup group = getById(groupId);
            if (group == null) {
                throw new RuntimeException("群组不存在");
            }

            // 检查权限：只有群主可以解散群组
            // Long currentUserId = SecurityUtils.getUserId();
            // if (!group.getOwnerId()
            //         .equals(currentUserId)) {
            //     throw new RuntimeException("只有群主可以解散群组");
            // }

            // 更新群组状态为解散
            group.setStatus("1");
            group.setUpdateTime(new Date());
            group.setUpdateBy(SecurityUtils.getUsername());
            boolean groupUpdated = updateById(group);

            // 逻辑删除所有群成员记录（设置状态为已离开）
            LambdaUpdateWrapper<ImGroupMember> memberUpdateWrapper = new LambdaUpdateWrapper<>();
            memberUpdateWrapper.eq(ImGroupMember::getGroupId, groupId)
                    .eq(ImGroupMember::getStatus, "0") // 只更新正常状态的成员
                    .set(ImGroupMember::getStatus, "1") // 设置为已离开
                    .set(ImGroupMember::getLeaveTime, new Date()) // 设置离开时间
                    .set(ImGroupMember::getLeaveReason, "群组解散") // 设置离开原因
                    .set(ImGroupMember::getUpdateTime, new Date()); // 更新时间
            boolean membersRemoved = groupMemberService.update(memberUpdateWrapper);

            return (groupUpdated && membersRemoved) ? 1 : 0;
        } catch (Exception e) {
            log.error("解散群组失败", e);
            throw new RuntimeException("解散群组失败: " + e.getMessage());
        }
    }



    @Override
    public List<ImGroupMember> getOnlineMembers(Long groupId) {
        // 这里需要调用网易云信API或其他在线状态服务
        // 暂时返回所有群成员，实际应该过滤在线状态
        LambdaQueryWrapper<ImGroupMember> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ImGroupMember::getGroupId, groupId)
                .orderByDesc(ImGroupMember::getJoinTime);
        return groupMemberService.list(wrapper);
    }

    @Override
    public List<Object> getOnlineMemberCount(List<Long> groupIds) {
        // 这里需要调用网易云信API或其他在线状态服务
        // 暂时返回群成员总数统计
        List<Object> results = new ArrayList<>();
        for (Long groupId : groupIds) {
            LambdaQueryWrapper<ImGroupMember> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ImGroupMember::getGroupId, groupId);
            long count = groupMemberService.count(wrapper);

            Map<String, Object> result = new HashMap<>();
            result.put("groupId", groupId);
            result.put("onlineCount", count); // 实际应该是在线人数
            result.put("totalCount", count);
            results.add(result);
        }
        return results;
    }

    @Override
    public List<ImGroupMember> selectGroupMembers(Long groupId, String keyword, String role, String status) {
        LambdaQueryWrapper<ImGroupMember> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ImGroupMember::getGroupId, groupId);

        if (StringUtils.isNotBlank(keyword)) {
            // 这里需要关联用户表进行昵称搜索，暂时按用户ID搜索
            try {
                Long userId = Long.parseLong(keyword);
                wrapper.eq(ImGroupMember::getUserId, userId);
            } catch (NumberFormatException e) {
                // 如果不是数字，暂时忽略关键字搜索
            }
        }

        if (StringUtils.isNotBlank(role)) {
            wrapper.eq(ImGroupMember::getRole, role);
        }

        if (StringUtils.isNotBlank(status)) {
            wrapper.eq(ImGroupMember::getStatus, status);
        }

        wrapper.orderByDesc(ImGroupMember::getJoinTime);

        return groupMemberService.list(wrapper);
    }

    @Override
    public GroupStatisticsResponseVo getGroupStatistics(Long groupId) {
        ImGroup group = getById(groupId);
        if (group == null) {
            return null;
        }

        // 统计群成员数
        LambdaQueryWrapper<ImGroupMember> memberWrapper = Wrappers.lambdaQuery();
        memberWrapper.eq(ImGroupMember::getGroupId, groupId);
        long memberCount = groupMemberService.count(memberWrapper);

        // 统计各角色成员数
        LambdaQueryWrapper<ImGroupMember> ownerWrapper = Wrappers.lambdaQuery();
        ownerWrapper.eq(ImGroupMember::getGroupId, groupId)
                .eq(ImGroupMember::getRole, "2");
        long ownerCount = groupMemberService.count(ownerWrapper);

        LambdaQueryWrapper<ImGroupMember> adminWrapper = Wrappers.lambdaQuery();
        adminWrapper.eq(ImGroupMember::getGroupId, groupId)
                .eq(ImGroupMember::getRole, "1");
        long adminCount = groupMemberService.count(adminWrapper);

        LambdaQueryWrapper<ImGroupMember> normalWrapper = Wrappers.lambdaQuery();
        normalWrapper.eq(ImGroupMember::getGroupId, groupId)
                .eq(ImGroupMember::getRole, "0");
        long normalCount = groupMemberService.count(normalWrapper);

        GroupStatisticsResponseVo vo = new GroupStatisticsResponseVo();
            vo.setGroupId(groupId);
            vo.setGroupName(group.getGroupName());
            vo.setTotalMembers(memberCount);
            vo.setOwnerCount(ownerCount);
            vo.setAdminCount(adminCount);
            vo.setNormalCount(normalCount);
            vo.setMaxMemberCount(group.getMaxMemberCount());
            vo.setCreateTime(group.getCreateTime());

        return vo;
    }


}
