package com.ruoyi.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 实例连接配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class QwxInstanceConnection implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连接ID
     */
    @TableId(value = "conn_id", type = IdType.AUTO)
    private Long connId;

    /**
     * 实例ID
     */
    private Long instanceId;

    /**
     * SaaS平台API地址
     */
    private String saasApiUrl;

    /**
     * 授权端点
     */
    private String authEndpoint;

    /**
     * 心跳端点
     */
    private String heartbeatEndpoint;

    /**
     * 数据同步端点
     */
    private String syncEndpoint;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥秘钥
     */
    private String apiSecret;

    /**
     * 连接超时(毫秒)
     */
    private Integer connTimeout;

    /**
     * 读取超时(毫秒)
     */
    private Integer readTimeout;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval;

    /**
     * 加密类型
     */
    private String encryptionType;

    /**
     * 加密密钥
     */
    private String encryptionKey;

    /**
     * 状态（0启用 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
