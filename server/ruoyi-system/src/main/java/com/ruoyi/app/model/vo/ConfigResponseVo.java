package com.ruoyi.app.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 配置响应VO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "配置响应参数", description = "配置信息响应参数")
public class ConfigResponseVo {

    /**
     * 配置ID
     */
    @ApiModelProperty(value = "配置ID", example = "1")
    private Long configId;

    /**
     * 租户ID
     */
    @ApiModelProperty(value = "租户ID", example = "1")
    private Long tenantId;

    /**
     * 配置键名
     */
    @ApiModelProperty(value = "配置键名", example = "netease.app.key")
    private String configKey;

    /**
     * 配置值（脱敏）
     */
    @ApiModelProperty(value = "配置值", example = "****")
    private String configValue;

    /**
     * 配置名称
     */
    @ApiModelProperty(value = "配置名称", example = "网易云信AppKey")
    private String configName;

    /**
     * 配置描述
     */
    @ApiModelProperty(value = "配置描述", example = "网易云信应用的AppKey")
    private String configDesc;

    /**
     * 配置类型
     */
    @ApiModelProperty(value = "配置类型", example = "text")
    private String configType;

    /**
     * 是否加密
     */
    @ApiModelProperty(value = "是否加密", example = "0")
    private String isEncrypted;

    /**
     * 是否系统配置
     */
    @ApiModelProperty(value = "是否系统配置", example = "1")
    private String isSystem;

    /**
     * 显示顺序
     */
    @ApiModelProperty(value = "显示顺序", example = "1")
    private Integer orderNum;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", example = "0")
    private String status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者", example = "admin")
    private String createBy;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者", example = "admin")
    private String updateBy;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", example = "配置备注信息")
    private String remark;
} 