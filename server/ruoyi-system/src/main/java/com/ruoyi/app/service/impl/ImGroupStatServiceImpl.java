package com.ruoyi.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.app.domain.ImGroupStat;
import com.ruoyi.app.mapper.ImGroupStatMapper;
import com.ruoyi.app.service.ImGroupStatService;
import com.ruoyi.app.service.ImGroupService;
import com.ruoyi.app.service.ImGroupMemberService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 群组统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-18
 */
@Service
public class ImGroupStatServiceImpl extends ServiceImpl<ImGroupStatMapper, ImGroupStat> implements ImGroupStatService {

    @Resource
    private ImGroupStatMapper imGroupStatMapper;
    
    @Resource
    private ImGroupService imGroupService;
    
    @Resource
    private ImGroupMemberService imGroupMemberService;

    @Override
    public boolean generateDailyStat(Long tenantId, Long groupId, Date statDate) {
        try {
            // 查询是否已存在统计记录
            LambdaQueryWrapper<ImGroupStat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImGroupStat::getTenantId, tenantId)
                       .eq(ImGroupStat::getGroupId, groupId)
                       .eq(ImGroupStat::getStatDate, statDate);
            
            ImGroupStat existingStat = getOne(queryWrapper);
            
            if (existingStat == null) {
                existingStat = new ImGroupStat();
                existingStat.setTenantId(tenantId);
                existingStat.setGroupId(groupId);
                existingStat.setStatDate(statDate);
                existingStat.setCreateTime(new Date());
            }
            
            // 计算统计数据
            // 这里需要根据实际业务逻辑来计算各项指标
            // 示例代码，实际需要根据业务需求调整
            
            existingStat.setUpdateTime(new Date());
            
            return saveOrUpdate(existingStat);
        } catch (Exception e) {
            log.error("生成群组每日统计数据失败", e);
            return false;
        }
    }

    @Override
    public int generateTenantDailyStat(Long tenantId, Date statDate) {
        // 查询租户下所有群组
        // 为每个群组生成统计数据
        // 这里需要根据实际业务逻辑实现
        return 0;
    }

    @Override
    public List<ImGroupStat> getStatsByGroupIdAndDateRange(Long tenantId, Long groupId, Date startDate, Date endDate) {
        return imGroupStatMapper.selectByGroupIdAndDateRange(tenantId, groupId, startDate, endDate);
    }

    @Override
    public List<ImGroupStat> getStatsByTenantIdAndDate(Long tenantId, Date statDate) {
        return imGroupStatMapper.selectByTenantIdAndDate(tenantId, statDate);
    }

    @Override
    public ImGroupStat getLatestStatByGroupId(Long tenantId, Long groupId) {
        return imGroupStatMapper.selectLatestByGroupId(tenantId, groupId);
    }

    @Override
    public boolean updateRealtimeStat(Long tenantId, Long groupId, Integer memberCount, Integer onlineCount) {
        try {
            // 获取今天的统计记录
            Date today = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String todayStr = sdf.format(today);
            Date statDate = sdf.parse(todayStr);
            
            LambdaQueryWrapper<ImGroupStat> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImGroupStat::getTenantId, tenantId)
                       .eq(ImGroupStat::getGroupId, groupId)
                       .eq(ImGroupStat::getStatDate, statDate);
            
            ImGroupStat stat = getOne(queryWrapper);
            if (stat == null) {
                stat = new ImGroupStat();
                stat.setTenantId(tenantId);
                stat.setGroupId(groupId);
                stat.setStatDate(statDate);
                stat.setCreateTime(new Date());
            }
            
            stat.setMemberCount(memberCount);
            stat.setOnlineCount(onlineCount);
            stat.setUpdateTime(new Date());
            
            return saveOrUpdate(stat);
        } catch (Exception e) {
            log.error("更新群组实时统计数据失败", e);
            return false;
        }
    }
} 