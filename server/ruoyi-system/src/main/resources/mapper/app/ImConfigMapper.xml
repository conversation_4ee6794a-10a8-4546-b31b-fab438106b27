<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImConfigMapper">

    <!-- 根据租户ID和配置键查询配置 -->
    <select id="selectByTenantIdAndKey" resultType="com.ruoyi.app.domain.ImConfig">
        SELECT config_id, tenant_id, config_key, config_value, config_name, config_desc, 
               config_type, is_encrypted, is_system, order_num, status, 
               create_time, update_time, create_by, update_by, remark
        FROM im_config
        WHERE tenant_id = #{tenantId}
        AND config_key = #{configKey}
        AND status = '0'
        LIMIT 1
    </select>

    <!-- 根据租户ID查询所有配置 -->
    <select id="selectByTenantId" resultType="com.ruoyi.app.domain.ImConfig">
        SELECT config_id, tenant_id, config_key, config_value, config_name, config_desc, 
               config_type, is_encrypted, is_system, order_num, status, 
               create_time, update_time, create_by, update_by, remark
        FROM im_config
        WHERE tenant_id = #{tenantId}
        AND status = '0'
        ORDER BY order_num ASC, config_id ASC
    </select>

    <!-- 查询系统配置 -->
    <select id="selectSystemConfigs" resultType="com.ruoyi.app.domain.ImConfig">
        SELECT config_id, tenant_id, config_key, config_value, config_name, config_desc, 
               config_type, is_encrypted, is_system, order_num, status, 
               create_time, update_time, create_by, update_by, remark
        FROM im_config
        WHERE is_system = '1'
        AND status = '0'
        ORDER BY order_num ASC, config_id ASC
    </select>

    <!-- 根据配置类型查询配置 -->
    <select id="selectByConfigType" resultType="com.ruoyi.app.domain.ImConfig">
        SELECT config_id, tenant_id, config_key, config_value, config_name, config_desc, 
               config_type, is_encrypted, is_system, order_num, status, 
               create_time, update_time, create_by, update_by, remark
        FROM im_config
        WHERE tenant_id = #{tenantId}
        AND config_type = #{configType}
        AND status = '0'
        ORDER BY order_num ASC, config_id ASC
    </select>

</mapper> 