<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxTenantConfigMapper">

  <!-- 根据租户ID和全局用户ID查询租户配置 -->
  <select id="selectByTenantIdAndGlobalUserId" resultType="com.ruoyi.app.domain.QwxTenantConfig">
    select * from qwx_tenant_config where tenant_id = #{tenantId}
  </select>    
</mapper> 