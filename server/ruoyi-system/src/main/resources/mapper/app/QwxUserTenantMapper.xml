<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxUserTenantMapper">

    <!-- 租户和云信账号信息结果映射 -->
    <resultMap id="TenantIMInfoResultMap" type="com.ruoyi.app.model.vo.AppUserTenantIMVo$TenantIMInfo">
        <!-- 租户信息 -->
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="tenant_code" property="tenantCode" />
        <result column="logo" property="logo" />
        <result column="industry" property="industry" />
        <result column="scale" property="scale" />
        <result column="address" property="address" />
        <result column="contact_user" property="contactUser" />
        <result column="contact_phone" property="contactPhone" />
        <result column="domain" property="domain" />
        <result column="intro" property="intro" />
        <result column="status" property="tenantStatus" />
        <result column="expire_time" property="expireTime" />
        <result column="account_count" property="accountCount" />

        <!-- 用户信息 -->
        <result column="is_admin" property="isAdmin" />
        <result column="nick_name" property="nickName" />
        <result column="avatar" property="avatar" />
        <result column="position" property="position" />
        <result column="signature" property="signature" />
        <result column="join_time" property="joinTime" />
        <result column="is_quit" property="isQuit" />
        
        <!-- 云信账号信息 -->
        <result column="im_id" property="imMappingId" />
        <result column="im_user_id" property="userId" />
        <result column="im_global_user_id" property="globalUserId" />
        <result column="yx_account_id" property="yxAccountId" />
        <result column="yx_token" property="yxToken" />
        <result column="im_status" property="imStatus" />
        <result column="im_create_time" property="createTime" />
        <result column="im_update_time" property="updateTime" />
    </resultMap>



    <select id="selectListByGlobalUserId" resultType="com.ruoyi.app.model.vo.UserJoinTenantInfoList">
        SELECT 
            ut.id, 
            ut.global_user_id, 
            ut.tenant_id, 
            ut.user_id, 
            ut.nick_name, 
            ut.avatar,
            ut.position,
            ut.signature,
            ut.join_time, 
            ut.status, 
            ut.online_status, 
            ut.is_default, 
            ut.create_time, 
            ut.update_time,
            t.tenant_name,
            t.tenant_code,
            t.logo
        FROM 
            qwx_user_tenant ut
        LEFT JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        ORDER BY 
            ut.is_default DESC, 
            ut.join_time DESC
    </select>

    <!-- 获取用户的所有租户及云信账号信息 -->
    <select id="selectUserTenantAndIMInfoList" resultMap="TenantIMInfoResultMap">
        SELECT 
            t.tenant_id,
            t.tenant_name,
            t.tenant_code,
            t.logo,
            t.industry,
            t.scale,
            t.address,
            t.contact_user,
            t.contact_phone,
            t.domain,
            t.intro,
            t.status,
            t.expire_time,
            t.account_count,
            ut.is_admin,
            ut.nick_name,
            ut.avatar,            
            ut.position,
            ut.signature,
            ut.join_time,
            ut.is_quit,
            i.id AS im_id,
            i.user_id AS im_user_id,
            i.global_user_id AS im_global_user_id,
            i.yx_account_id,
            i.yx_token,
            i.status AS im_status,
            i.create_time AS im_create_time,
            i.update_time AS im_update_time
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        LEFT JOIN 
            im_yxaccount_mapping i ON ut.global_user_id = i.global_user_id AND ut.tenant_id = i.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        ORDER BY 
            ut.join_time DESC
    </select>
    
    <!-- 获取用户的指定租户及云信账号信息 -->
    <select id="selectSingleUserTenantAndIMInfo" resultMap="TenantIMInfoResultMap">
        SELECT 
            t.tenant_id,
            t.tenant_name,
            t.tenant_code,
            t.logo,
            t.industry,
            t.scale,
            t.address,
            t.contact_user,
            t.contact_phone,
            t.domain,
            t.intro,
            t.status,
            t.expire_time,
            t.account_count,
            ut.is_admin,
            ut.nick_name,
            ut.avatar,            
            ut.position,
            ut.signature,
            ut.join_time,
            ut.is_quit,
            i.id AS im_id,
            i.user_id AS im_user_id,
            i.global_user_id AS im_global_user_id,
            i.yx_account_id,
            i.yx_token,
            i.status AS im_status,
            i.create_time AS im_create_time,
            i.update_time AS im_update_time
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_tenant t ON ut.tenant_id = t.tenant_id
        LEFT JOIN 
            im_yxaccount_mapping i ON ut.global_user_id = i.global_user_id AND ut.tenant_id = i.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND ut.tenant_id = #{tenantId}
            AND ut.status = '0'
            AND ut.is_quit = '0'
            AND t.status = '0'
            AND t.del_flag = '0'
        LIMIT 1
    </select>

    <!-- 查询租户下用户详细信息（包含联系方式） -->
    <select id="selectTenantUserDetailList" resultType="com.ruoyi.app.model.vo.QwxUserTenantDetailVo">
        SELECT 
            ut.id,
            ut.global_user_id,
            ut.tenant_id,
            ut.user_id,
            ut.nick_name,
            ut.avatar,
            ut.position,
            ut.signature,
            ut.join_time,
            ut.status,
            ut.online_status,
            ut.is_admin,
            ut.create_time,
            ut.update_time,
            ut.is_quit,
            gu.user_name,
            gu.phone,
            gu.email
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_global_user gu ON ut.global_user_id = gu.global_user_id
        WHERE 
            ut.tenant_id = #{tenantId}
            AND ut.is_quit = '0'
            <if test="status != null and status != ''">
                AND ut.status = #{status}
            </if>
            AND gu.del_flag = '0'
        ORDER BY 
            ut.is_admin DESC,
            ut.join_time DESC
    </select>

    <!-- 查询租户下用户详细信息（支持查询条件） -->
    <select id="selectTenantUserDetailListWithConditions" resultType="com.ruoyi.app.model.vo.QwxUserTenantDetailVo">
        SELECT 
            ut.id,
            ut.global_user_id,
            ut.tenant_id,
            ut.user_id,
            ut.nick_name,
            ut.avatar,
            ut.position,
            ut.signature,
            ut.join_time,
            ut.status,
            ut.online_status,
            ut.is_admin,
            ut.create_time,
            ut.update_time,
            ut.is_quit,
            gu.user_name,
            gu.phone,
            gu.email
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_global_user gu ON ut.global_user_id = gu.global_user_id
        WHERE 
            ut.is_quit = '0'
            AND gu.del_flag = '0'
            <if test="qwxUserTenant.tenantId != null">
                AND ut.tenant_id = #{qwxUserTenant.tenantId}
            </if>
            <if test="qwxUserTenant.nickName != null and qwxUserTenant.nickName != ''">
                AND ut.nick_name LIKE CONCAT('%', #{qwxUserTenant.nickName}, '%')
            </if>
            <if test="qwxUserTenant.position != null and qwxUserTenant.position != ''">
                AND ut.position LIKE CONCAT('%', #{qwxUserTenant.position}, '%')
            </if>
            <if test="qwxUserTenant.signature != null and qwxUserTenant.signature != ''">
                AND ut.signature LIKE CONCAT('%', #{qwxUserTenant.signature}, '%')
            </if>
            <if test="qwxUserTenant.status != null and qwxUserTenant.status != ''">
                AND ut.status = #{qwxUserTenant.status}
            </if>
        ORDER BY 
            ut.is_admin DESC,
            ut.join_time DESC
    </select>

    <!-- 获取租户用户详细信息 -->
    <select id="getTenantUserDetail" resultType="com.ruoyi.app.model.vo.QwxUserTenantDetailVo">
        SELECT 
            ut.id,
            ut.global_user_id,
            ut.tenant_id,
            ut.user_id,
            ut.nick_name,
            ut.avatar,
            ut.position,
            ut.signature,
            ut.join_time,
            ut.status,
            ut.online_status,
            ut.is_admin,
            ut.create_time,
            ut.update_time,
            ut.is_quit,
            gu.user_name,
            gu.phone,
            gu.email
        FROM 
            qwx_user_tenant ut
        INNER JOIN 
            qwx_global_user gu ON ut.global_user_id = gu.global_user_id
        WHERE 
            ut.tenant_id = #{tenantId}
            AND ut.user_id = #{userId}
            AND ut.is_quit = '0'
            AND gu.del_flag = '0'
    </select>
</mapper>
