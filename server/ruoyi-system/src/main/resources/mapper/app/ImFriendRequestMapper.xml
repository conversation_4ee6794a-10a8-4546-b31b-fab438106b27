<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImFriendRequestMapper">

    <!-- 根据接收者用户ID查询好友申请列表 -->
    <select id="selectByReceiverUserId" resultType="com.ruoyi.app.domain.ImFriendRequest">
        SELECT id, tenant_id, sender_user_id, receiver_user_id, request_message, status, request_time, response_time, create_time, update_time
        FROM im_friend_request
        WHERE tenant_id = #{tenantId}
        AND receiver_user_id = #{receiverUserId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        ORDER BY request_time DESC
    </select>

    <!-- 根据发送者和接收者查询好友申请记录 -->
    <select id="selectBySenderAndReceiver" resultType="com.ruoyi.app.domain.ImFriendRequest">
        SELECT id, tenant_id, sender_user_id, receiver_user_id, request_message, status, request_time, response_time, create_time, update_time
        FROM im_friend_request
        WHERE tenant_id = #{tenantId}
        AND sender_user_id = #{senderUserId}
        AND receiver_user_id = #{receiverUserId}
        ORDER BY request_time DESC
        LIMIT 1
    </select>

</mapper>
