<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupOpLogMapper">

    <!-- 根据群组ID查询操作日志 -->
    <select id="selectByGroupId" resultType="com.ruoyi.app.domain.ImGroupOpLog">
        SELECT log_id, tenant_id, group_id, operator_id, target_user_id, operation_type, 
               operation_desc, before_data, after_data, client_ip, user_agent, 
               create_time, status, error_msg
        FROM im_group_op_log
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据操作人ID查询操作日志 -->
    <select id="selectByOperatorId" resultType="com.ruoyi.app.domain.ImGroupOpLog">
        SELECT log_id, tenant_id, group_id, operator_id, target_user_id, operation_type, 
               operation_desc, before_data, after_data, client_ip, user_agent, 
               create_time, status, error_msg
        FROM im_group_op_log
        WHERE tenant_id = #{tenantId}
        AND operator_id = #{operatorId}
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper> 