<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupMapper">

    <!-- 根据租户ID查询群组列表 -->
    <select id="selectByTenantId" resultType="com.ruoyi.app.domain.ImGroup">
        SELECT group_id, cloud_group_id, tenant_id, group_name, group_type, group_avatar, 
               owner_id, introduction, announcement, member_count, max_member_count, 
               join_mode, mute_all, invite_mode, update_info_mode, extension, status, 
               yx_group_id, team_mute_type, be_invite_mode, update_custom_mode, 
               custom_field, server_custom_field, create_time, create_by, update_time, update_by
        FROM im_group
        WHERE tenant_id = #{tenantId}
        AND status = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 根据群主ID查询群组列表 -->
    <select id="selectByOwnerId" resultType="com.ruoyi.app.domain.ImGroup">
        SELECT group_id, cloud_group_id, tenant_id, group_name, group_type, group_avatar, 
               owner_id, introduction, announcement, member_count, max_member_count, 
               join_mode, mute_all, invite_mode, update_info_mode, extension, status, 
               yx_group_id, team_mute_type, be_invite_mode, update_custom_mode, 
               custom_field, server_custom_field, create_time, create_by, update_time, update_by
        FROM im_group
        WHERE tenant_id = #{tenantId}
        AND owner_id = #{ownerId}
        AND status = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 根据云信群组ID查询群组 -->
    <select id="selectByYxGroupId" resultType="com.ruoyi.app.domain.ImGroup">
        SELECT group_id, cloud_group_id, tenant_id, group_name, group_type, group_avatar, 
               owner_id, introduction, announcement, member_count, max_member_count, 
               join_mode, mute_all, invite_mode, update_info_mode, extension, status, 
               yx_group_id, team_mute_type, be_invite_mode, update_custom_mode, 
               custom_field, server_custom_field, create_time, create_by, update_time, update_by
        FROM im_group
        WHERE tenant_id = #{tenantId}
        AND yx_group_id = #{yxGroupId}
        LIMIT 1
    </select>

    <!-- 更新群组成员数量 -->
    <update id="updateGroupMemberCount">
        UPDATE im_group 
        SET member_count = (
            SELECT COUNT(*) 
            FROM im_group_member 
            WHERE group_id = #{groupId} 
            AND status = '0'
        ),
        update_time = NOW()
        WHERE group_id = #{groupId}
    </update>

</mapper>
