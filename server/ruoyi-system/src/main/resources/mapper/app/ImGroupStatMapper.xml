<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupStatMapper">

    <!-- 根据群组ID和日期范围查询统计数据 -->
    <select id="selectByGroupIdAndDateRange" resultType="com.ruoyi.app.domain.ImGroupStat">
        SELECT stat_id, tenant_id, group_id, stat_date, member_count, online_count, 
               msg_count, active_member_count, join_count, leave_count, create_time, update_time
        FROM im_group_stat
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        <if test="startDate != null">
            AND stat_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND stat_date &lt;= #{endDate}
        </if>
        ORDER BY stat_date DESC
    </select>

    <!-- 根据租户ID和日期查询所有群组统计数据 -->
    <select id="selectByTenantIdAndDate" resultType="com.ruoyi.app.domain.ImGroupStat">
        SELECT stat_id, tenant_id, group_id, stat_date, member_count, online_count, 
               msg_count, active_member_count, join_count, leave_count, create_time, update_time
        FROM im_group_stat
        WHERE tenant_id = #{tenantId}
        AND stat_date = #{statDate}
        ORDER BY member_count DESC
    </select>

    <!-- 获取群组最新统计数据 -->
    <select id="selectLatestByGroupId" resultType="com.ruoyi.app.domain.ImGroupStat">
        SELECT stat_id, tenant_id, group_id, stat_date, member_count, online_count, 
               msg_count, active_member_count, join_count, leave_count, create_time, update_time
        FROM im_group_stat
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        ORDER BY stat_date DESC
        LIMIT 1
    </select>

</mapper> 