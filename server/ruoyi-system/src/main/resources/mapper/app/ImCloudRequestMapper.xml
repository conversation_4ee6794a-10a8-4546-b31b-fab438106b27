<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImCloudRequestMapper">

    <!-- 根据请求UUID查询记录 -->
    <select id="selectByRequestUuid" resultType="com.ruoyi.app.domain.ImCloudRequest">
        SELECT request_id, tenant_id, request_uuid, api_name, request_url, request_method, 
               request_headers, request_body, response_code, response_body, cost_time, 
               retry_count, status, error_msg, create_time, finish_time
        FROM im_cloud_request
        WHERE request_uuid = #{requestUuid}
        LIMIT 1
    </select>

    <!-- 根据租户ID和接口名称查询记录 -->
    <select id="selectByTenantIdAndApi" resultType="com.ruoyi.app.domain.ImCloudRequest">
        SELECT request_id, tenant_id, request_uuid, api_name, request_url, request_method, 
               request_headers, request_body, response_code, response_body, cost_time, 
               retry_count, status, error_msg, create_time, finish_time
        FROM im_cloud_request
        WHERE tenant_id = #{tenantId}
        <if test="apiName != null and apiName != ''">
            AND api_name = #{apiName}
        </if>
        <if test="startTime != null">
            AND create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND create_time &lt;= #{endTime}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询失败的请求记录 -->
    <select id="selectFailedRequests" resultType="com.ruoyi.app.domain.ImCloudRequest">
        SELECT request_id, tenant_id, request_uuid, api_name, request_url, request_method, 
               request_headers, request_body, response_code, response_body, cost_time, 
               retry_count, status, error_msg, create_time, finish_time
        FROM im_cloud_request
        WHERE tenant_id = #{tenantId}
        AND status = '2'
        AND retry_count &lt; #{maxRetryCount}
        ORDER BY create_time ASC
    </select>

    <!-- 清理过期记录 -->
    <delete id="deleteExpiredRecords">
        DELETE FROM im_cloud_request
        WHERE create_time &lt; #{expireTime}
    </delete>

</mapper> 