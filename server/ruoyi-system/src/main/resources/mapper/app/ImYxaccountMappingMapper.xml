<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImYxaccountMappingMapper">

    <!-- 根据全局用户ID和租户ID查询云信账号信息 -->
    <select id="selectByGlobalUserIdAndTenantId" resultType="com.ruoyi.app.domain.ImYxaccountMapping">
        SELECT 
        id, user_id, global_user_id, tenant_id, yx_account_id, yx_token, status, create_time, update_time
        FROM im_yxaccount_mapping
        WHERE global_user_id = #{globalUserId}
        AND tenant_id = #{tenantId}
        AND status = '0'
        LIMIT 1
    </select>

</mapper>
