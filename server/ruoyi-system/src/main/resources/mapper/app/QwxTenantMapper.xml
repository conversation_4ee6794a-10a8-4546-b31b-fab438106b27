<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.QwxTenantMapper">

    <select id="selectQwxTenantList" parameterType="QwxTenant" resultType="com.ruoyi.app.domain.QwxTenant">
        select t.tenant_id, t.tenant_name, t.tenant_code, t.logo, t.industry, t.scale, t.address, t.contact_user, t.contact_phone, t.domain, t.intro, t.status, t.del_flag, t.expire_time, t.account_count, t.create_user_id, t.create_time, t.update_time, t.remark
        from qwx_tenant t
        <where>
            t.del_flag = '0'
            <if test="tenantName != null  and tenantName != ''"> and t.tenant_name like concat('%', #{tenantName}, '%')</if>
            <if test="tenantCode != null  and tenantCode != ''"> and t.tenant_code = #{tenantCode}</if>
            <if test="logo != null  and logo != ''"> and t.logo = #{logo}</if>
            <if test="industry != null  and industry != ''"> and t.industry = #{industry}</if>
            <if test="scale != null  and scale != ''"> and t.scale = #{scale}</if>
            <if test="address != null  and address != ''"> and t.address = #{address}</if>
            <if test="contactUser != null  and contactUser != ''"> and t.contact_user = #{contactUser}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and t.contact_phone = #{contactPhone}</if>
            <if test="domain != null  and domain != ''"> and t.domain = #{domain}</if>
            <if test="intro != null  and intro != ''"> and t.intro = #{intro}</if>
            <if test="status != null  and status != ''"> and t.status = #{status}</if>
            <if test="expireTime != null "> and t.expire_time = #{expireTime}</if>
            <if test="accountCount != null "> and t.account_count = #{accountCount}</if>
            <if test="createUserId != null  and createUserId != ''"> and t.create_user_id = #{createUserId}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by t.create_time desc
    </select>

    <select id="selectQwxTenantByTenantId" parameterType="Long" resultType="com.ruoyi.app.domain.QwxTenant">
        select tenant_id, tenant_name, tenant_code, logo, industry, scale, address, contact_user, contact_phone, domain, intro, status, del_flag, expire_time, account_count, create_user_id, create_time, update_time, remark from qwx_tenant
        where tenant_id = #{tenantId}
    </select>

    <select id="selectTenantListByGlobalUserId" resultType="com.ruoyi.app.domain.QwxTenant">
        SELECT 
            t.*
        FROM 
            qwx_tenant t
        INNER JOIN 
            qwx_user_tenant ut ON t.tenant_id = ut.tenant_id
        WHERE 
            ut.global_user_id = #{globalUserId}
            AND t.status = '0' 
            AND t.del_flag = '0'
    </select>
</mapper>
