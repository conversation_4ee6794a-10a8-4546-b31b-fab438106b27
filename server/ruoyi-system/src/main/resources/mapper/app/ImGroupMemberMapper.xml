<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.app.mapper.ImGroupMemberMapper">

    <!-- 根据群组ID查询成员列表 -->
    <select id="selectByGroupId" resultType="com.ruoyi.app.domain.ImGroupMember">
        SELECT id, tenant_id, group_id, user_id, invite_user_id, join_source, nickname, 
               role, join_time, leave_time, leave_reason, mute_end_time, status, create_time, update_time
        FROM im_group_member
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        AND status = '0'
        ORDER BY role DESC, join_time ASC
    </select>

    <!-- 根据用户ID查询加入的群组列表 -->
    <select id="selectByUserId" resultType="com.ruoyi.app.domain.ImGroupMember">
        SELECT id, tenant_id, group_id, user_id, invite_user_id, join_source, nickname, 
               role, join_time, leave_time, leave_reason, mute_end_time, status, create_time, update_time
        FROM im_group_member
        WHERE tenant_id = #{tenantId}
        AND user_id = #{userId}
        AND status = '0'
        ORDER BY join_time DESC
    </select>

    <!-- 查询群组成员数量 -->
    <select id="countByGroupId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM im_group_member
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        AND status = '0'
    </select>

    <!-- 根据群组ID和用户ID查询成员信息 -->
    <select id="selectByGroupIdAndUserId" resultType="com.ruoyi.app.domain.ImGroupMember">
        SELECT id, tenant_id, group_id, user_id, invite_user_id, join_source, nickname, 
               role, join_time, leave_time, leave_reason, mute_end_time, status, create_time, update_time
        FROM im_group_member
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        AND user_id = #{userId}
        LIMIT 1
    </select>

    <!-- 查询群组管理员列表 -->
    <select id="selectAdminsByGroupId" resultType="com.ruoyi.app.domain.ImGroupMember">
        SELECT id, tenant_id, group_id, user_id, invite_user_id, join_source, nickname, 
               role, join_time, leave_time, leave_reason, mute_end_time, status, create_time, update_time
        FROM im_group_member
        WHERE tenant_id = #{tenantId}
        AND group_id = #{groupId}
        AND role IN ('1', '2')
        AND status = '0'
        ORDER BY role DESC, join_time ASC
    </select>

    <!-- 查询群成员列表（包含用户详细信息） -->
    <select id="selectMemberListWithUserDetails" resultType="com.ruoyi.app.model.vo.QwxUserTenantDetailVo">
        SELECT 
            gm.user_id AS userId,
            gu.user_name AS userName,
            ut.global_user_id AS globalUserId,
            gm.nickname AS nickName,
            ut.avatar,
            ut.position,
            ut.signature,
            gu.phone,
            gu.email,
            ut.is_admin AS isAdmin,
            gm.role AS role,
            gm.join_time AS joinTime,
            gm.status AS status
        FROM im_group_member gm
        LEFT JOIN qwx_user_tenant ut ON gm.tenant_id = ut.tenant_id AND gm.user_id = ut.user_id
        LEFT JOIN qwx_global_user gu ON ut.global_user_id = gu.global_user_id
        WHERE gm.group_id = #{groupId}
        AND gm.status = '0'
        <if test="keyword != null and keyword != ''">
            AND (gm.nickname LIKE CONCAT('%', #{keyword}, '%') 
                 OR gu.user_name LIKE CONCAT('%', #{keyword}, '%')
                 OR gu.phone LIKE CONCAT('%', #{keyword}, '%')
                 OR gu.email LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="role != null and role != ''">
            AND gm.role = #{role}
        </if>
        ORDER BY gm.role DESC, gm.join_time ASC
    </select>

</mapper>
